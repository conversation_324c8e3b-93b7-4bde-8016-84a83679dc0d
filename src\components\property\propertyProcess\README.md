# Property Processors

属性处理器模块，用于处理右侧面板中的属性变化并将其应用到具体的对象上。

## 架构概述

每个属性组件都有对应的处理器：

- **BaseInfoProcessor** - 处理基础信息组件的属性
- **ImageInfoProcessor** - 处理图片信息组件的属性  
- **SpriteColorProcessor** - 处理Sprite颜色组件的属性
- **TextInfoProcessor** - 处理文字信息组件的属性
- **FilterProcessor** - 处理滤镜组件的属性

## 使用方法

### 1. 统一处理器（推荐）

```typescript
import { PropertyProcessor } from '../propertyProcess';

// 应用单个属性
PropertyProcessor.applyProperty(object, 'x', 100);
PropertyProcessor.applyProperty(object, 'hue', 180);
PropertyProcessor.applyProperty(object, 'text', 'Hello World');

// 批量应用属性
PropertyProcessor.applyProperties(object, {
  x: 100,
  y: 50,
  width: 200,
  height: 100,
  alpha: 0.8,
  hue: 180,
  text: 'Hello World'
});
```

### 2. 单独使用处理器

```typescript
import { 
  BaseInfoProcessor, 
  SpriteColorProcessor,
  TextInfoProcessor 
} from '../propertyProcess';

// 基础信息
BaseInfoProcessor.applyProperty(object, 'x', 100);
BaseInfoProcessor.applyProperties(object, {
  x: 100,
  y: 50,
  width: 200,
  height: 100
});

// Sprite颜色
SpriteColorProcessor.applyProperty(object, 'hue', 180);
SpriteColorProcessor.applyProperties(object, {
  hue: 180,
  colorTone: [50, 0, -50, 0],
  blendColor: [255, 0, 0, 128]
});

// 文字信息
TextInfoProcessor.applyProperty(object, 'text', 'Hello');
TextInfoProcessor.applyProperties(object, {
  text: 'Hello World',
  fontSize: 32,
  textColor: '#ff0000'
});
```

## 各处理器详细说明

### BaseInfoProcessor

处理基础属性：
- `x`, `y` - 位置
- `width`, `height` - 尺寸
- `scaleX`, `scaleY` - 缩放
- `rotation` - 旋转
- `alpha` - 透明度
- `visible` - 可见性
- `zIndex` - 层级
- `name` - 名称

### ImageInfoProcessor

处理图片相关属性：
- `texture` - 纹理路径
- `bitmap._url` - 位图URL
- `anchor` - 锚点 `{x: number, y: number}`
- `tint` - 色调
- `blendMode` - 混合模式
- `frame` - 帧区域 `{x, y, width, height}`

### SpriteColorProcessor

处理Sprite颜色属性：
- `hue` - 色相 (0-360)
- `colorTone` - 色调 `[red, green, blue, gray]`
- `blendColor` - 混合颜色 `[red, green, blue, alpha]`
- `tint` - PIXI色调
- `alpha` - 透明度

### TextInfoProcessor

处理文字属性：
- `text` - 文字内容
- `fontSize` - 字体大小
- `fontFace` - 字体名称
- `fontBold`, `fontItalic` - 字体样式
- `textColor`, `outlineColor` - 颜色
- `outlineWidth` - 描边宽度
- `textAlign`, `textBaseline` - 对齐方式
- `wordWrap`, `wordWrapWidth` - 自动换行
- `lineHeight` - 行高

### FilterProcessor

处理滤镜属性：
- `filters` - 滤镜数组
- `filters.add` - 添加滤镜 `{type: string, params: any}`
- `filters.remove` - 移除滤镜 `{index: number}`
- `filters.modify` - 修改滤镜 `{index: number, params: any}`
- `filters.reorder` - 重排滤镜 `{fromIndex: number, toIndex: number}`

## 在组件中使用

### 在属性组件中使用

```typescript
// 在baseInfoGroup.tsx中
import { PropertyProcessor } from '../../propertyProcess';

const handlePropertyChange = (propertyName: string, value: any) => {
  // 应用到所有选中的对象
  selectedObjects.forEach(object => {
    PropertyProcessor.applyProperty(object, propertyName, value);
  });
};
```

### 在Store中集成

```typescript
// 在Store.ts中
import { PropertyProcessor } from '../propertyProcess';

setSelectedObjectsProperty: (fieldName: string, newValue: any) => {
  const state = get();
  
  // 使用PropertyProcessor处理属性变化
  state.selectedObjects.objects.forEach(obj => {
    PropertyProcessor.applyProperty(obj, fieldName, newValue);
  });
}
```

## 特性

1. **自动选择处理器** - 根据属性名自动选择合适的处理器
2. **实时预览** - 直接修改对象属性，立即看到效果
3. **后端记录** - 自动记录到generators系统，用于代码生成
4. **类型安全** - 完整的TypeScript类型定义
5. **错误处理** - 完善的错误处理和日志记录
6. **验证机制** - 属性值验证和默认值支持

## 扩展

要添加新的属性处理器：

1. 创建新的处理器文件（如 `CustomProcessor.ts`）
2. 实现 `applyProperty` 和 `applyProperties` 方法
3. 在 `index.ts` 中导出并添加到 `getProcessorForProperty` 方法中
4. 更新 `AllProperties` 类型定义

```typescript
// CustomProcessor.ts
export class CustomProcessor {
  static applyProperty(object: any, propertyName: string, value: any): void {
    // 实现自定义属性处理逻辑
  }
  
  static applyProperties(object: any, properties: any): void {
    // 实现批量属性处理逻辑
  }
}
```
