# Generators架构迁移指南

## 概述

我们已经完全重新设计并实现了generators架构，删除了所有旧代码，统一使用新的BaseTempObj API。

## 新架构特点

### 1. 核心设计
- **BaseTempObj类**：代表一个修改的对象，包含对象的生成代码面板
- **OperationManager类**：管理所有的BaseTempObj操作
- **只有字段值不为null**：说明发生了变化，需要生成修改代码
- **完整的序列化支持**：可以保存和加载操作记录

### 2. 文件结构
```
src/generators/
├── core/
│   ├── baseTempObj.ts        # 核心BaseTempObj类
│   └── OperationManager.ts   # 操作管理器
├── test/
│   └── TestNewArchitecture.ts # 测试文件
└── index.ts                  # 主入口文件

src/services/
└── index.ts                  # 统一的服务层API
```

## API迁移对照表

### 旧API → 新API

#### 属性设置
```typescript
// 旧方式
recordPropertyModification(object, 'x', 100);
updateBasicProperty(object, 'x', 100);

// 新方式
setObjectProperty(['Scene_Title', '2'], 'Sprite', 'x', 100);
// 或使用PropertyService
PropertyService.setProperty(object, 'x', 100, ['Scene_Title', '2'], 'Sprite');
```

#### 代码生成
```typescript
// 旧方式
const generator = new PropertyGenerator();
const code = generator.generate(operation);

// 新方式
const code = generateAllCode();
// 或按场景生成
const sceneCode = generateCodeByScene();
```

#### 操作管理
```typescript
// 旧方式
const queue = new OperationQueue();
queue.addOperation(operation);

// 新方式
const operation = createOperation(['Scene_Title', '2'], 'Sprite');
operation.setProperty('x', 100);
```

## 使用示例

### 1. 基本属性设置
```typescript
import { setObjectProperty, generateAllCode } from '../generators';

// 设置属性
setObjectProperty(['Scene_Title', '2'], 'Sprite', 'x', 100);
setObjectProperty(['Scene_Title', '2'], 'Sprite', 'y', 200);
setObjectProperty(['Scene_Title', '2'], 'Sprite', 'alpha', 0.8);

// 生成代码
const code = generateAllCode();
console.log(code);
```

### 2. bitmap.elements处理
```typescript
import { createOperation } from '../generators';

const operation = createOperation(['Scene_Menu', '1', '3', '1', '2'], 'Sprite');

const elementsValue = [
  {
    "type": "text",
    "text": "里德",
    "x": 184,
    "y": 13
  },
  {
    "type": "image",
    "source": {
      "_url": "../projects/Project4/img/faces/Actor1.png",
      "width": 144,
      "height": 129
    },
    "sx": 432,
    "sy": 7,
    "sw": 144,
    "sh": 129
  }
];

operation.setProperty('_bitmap.elements', elementsValue);
const code = operation.generateCode();
```

### 3. 颜色属性
```typescript
import { createOperation } from '../generators';

const operation = createOperation(['Scene_Title', '3'], 'Sprite');
operation.setProperty('colorTone', [255, 0, 0, 128]);
operation.setProperty('blendColor', [0, 255, 0, 64]);

const code = operation.generateCode();
```

### 4. 序列化和保存
```typescript
import { serializeOperations, deserializeOperations } from '../generators';

// 序列化
const jsonData = serializeOperations();

// 保存到文件（需要配合后端）
// await saveToFile(jsonData);

// 加载和反序列化
// const loadedData = await loadFromFile();
deserializeOperations(loadedData);
```

### 5. 使用PropertyService
```typescript
import { PropertyService } from '../services';

// 设置单个属性
PropertyService.setProperty(sprite, 'x', 100, ['Scene_Title', '2'], 'Sprite');

// 批量设置属性
PropertyService.setProperties(sprite, {
  x: 100,
  y: 200,
  alpha: 0.8
}, ['Scene_Title', '2'], 'Sprite');

// 生成代码
const code = PropertyService.generateCode();

// 保存操作记录
const serializedData = PropertyService.saveOperations();
```

## 生成的代码格式

新架构生成的代码格式：

```javascript
// 设置 Sprite 的 3 个属性
const target_sprite_Scene_Title_2Path = ["Scene_Title", "2"];
const target_sprite_Scene_Title_2 = findObjectByScenePath(target_sprite_Scene_Title_2Path);
if (target_sprite_Scene_Title_2) {
    target_sprite_Scene_Title_2.x = 100;
    if (DEBUG) console.log('设置属性:', 'x', 100, target_sprite_Scene_Title_2);
    target_sprite_Scene_Title_2.y = 200;
    if (DEBUG) console.log('设置属性:', 'y', 200, target_sprite_Scene_Title_2);
    target_sprite_Scene_Title_2.alpha = 0.8;
    if (DEBUG) console.log('设置属性:', 'alpha', 0.8, target_sprite_Scene_Title_2);
}
```

## 关键改进

1. **简化架构**：删除了复杂的生成器层次结构
2. **统一管理**：所有操作都通过OperationManager管理
3. **正确的bitmap处理**：修复了source分离问题，source就是bitmap
4. **完整的序列化**：支持保存和恢复操作记录
5. **便捷的API**：提供了丰富的便捷方法
6. **按场景分组**：支持按场景生成代码
7. **统计信息**：提供详细的操作统计

## 注意事项

1. **所有旧的API都已删除**，必须使用新的API
2. **bitmap.elements处理已修复**：不再分离source，直接设置完整数组
3. **序列化格式已改变**：旧的序列化数据无法直接使用
4. **代码生成逻辑已简化**：更加直接和可靠
5. **统一使用新的服务层**：通过PropertyService访问所有功能

## 测试

运行测试以验证新架构：

```typescript
import { testNewArchitecture } from '../generators/test/TestNewArchitecture';

testNewArchitecture();
```

这将测试所有新功能，包括属性设置、代码生成、序列化等。
