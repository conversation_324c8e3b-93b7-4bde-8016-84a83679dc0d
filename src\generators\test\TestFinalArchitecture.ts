/**
 * 测试最终的generators架构
 * 验证：
 * 1. 删除BaseTempObjData，modifiedProperties使用Map存储字段和值
 * 2. BackendService先调用OperationManager方法，再调用后端API
 * 3. OperationManager的统一合成代码、查找对象方法和生成对象方法面板代码
 */

import { 
  BaseTempObj, 
  OperationManager,
  setObjectProperty,
  createOperation
} from '../index';

import { BackendService } from '../../services/BackendService';

export function testFinalArchitecture(): void {
  console.log('🧪 测试最终的generators架构...');
  
  // 测试1: BaseTempObj使用Map存储属性
  console.log('\n1. 测试BaseTempObj使用Map存储属性...');
  const obj1 = new BaseTempObj(['Scene_Title', '2'], 'Sprite', 'modify');
  
  // 设置属性
  obj1.setProperty('x', 100);
  obj1.setProperty('y', 200);
  obj1.setProperty('alpha', 0.8);
  obj1.setProperty('visible', true);
  
  console.log('- 修改的属性名称集合:', obj1.getModifiedPropertyNames());
  console.log('- 修改的属性详情:', obj1.getModifiedProperties());
  
  // 验证Map存储
  console.log('- 获取x属性值:', obj1.getProperty('x'));
  console.log('- 获取不存在的属性:', obj1.getProperty('nonexistent'));
  
  // 清除一个属性
  obj1.clearPropertyModification('y');
  console.log('- 清除y属性后的修改集合:', obj1.getModifiedPropertyNames());
  
  // 测试2: 序列化和反序列化
  console.log('\n2. 测试序列化和反序列化...');
  const serialized = obj1.toJSON();
  console.log('- 序列化结果:', serialized);
  
  const obj2 = BaseTempObj.fromJSON(serialized);
  console.log('- 反序列化后的属性:', obj2.getModifiedPropertyNames());
  console.log('- 反序列化后的x值:', obj2.getProperty('x'));
  
  // 测试3: OperationManager的统一合成代码
  console.log('\n3. 测试OperationManager的统一合成代码...');
  const manager = new OperationManager();
  
  // 添加多个场景的操作
  manager.addOperation(['Scene_Title', '1'], 'Sprite');
  manager.setProperty(['Scene_Title', '1'], 'Sprite', 'x', 50);
  manager.setProperty(['Scene_Title', '1'], 'Sprite', 'y', 75);
  manager.setProperty(['Scene_Title', '1'], 'Sprite', 'alpha', 0.9);
  
  manager.addOperation(['Scene_Menu', '2'], 'Container');
  manager.setProperty(['Scene_Menu', '2'], 'Container', 'visible', false);
  manager.setProperty(['Scene_Menu', '2'], 'Container', 'rotation', 0.5);
  
  manager.addOperation(['Scene_Battle', '3'], 'Sprite');
  manager.setProperty(['Scene_Battle', '3'], 'Sprite', 'tint', 0xff0000);
  manager.setProperty(['Scene_Battle', '3'], 'Sprite', 'colorTone', [255, 0, 0, 128]);
  
  // 添加bitmap.elements操作
  const elementsValue = [
    {
      "type": "text",
      "text": "测试文本",
      "x": 10,
      "y": 10
    },
    {
      "type": "image",
      "source": {
        "_url": "../projects/Test/img/faces/Actor1.png",
        "width": 144,
        "height": 129
      },
      "sx": 0,
      "sy": 0,
      "sw": 144,
      "sh": 129,
      "dx": 50,
      "dy": 50,
      "dw": 144,
      "dh": 129
    }
  ];
  
  manager.addOperation(['Scene_Status', '4'], 'Sprite');
  manager.setProperty(['Scene_Status', '4'], 'Sprite', '_bitmap.elements', elementsValue);
  
  // 生成统一合成代码
  const unifiedCode = manager.generateUnifiedCode();
  console.log('统一合成代码长度:', unifiedCode.length);
  
  // 验证代码包含必要的方法
  const hasFindObjectMethod = unifiedCode.includes('function findObjectByScenePath');
  const hasCreateGameObjectMethod = unifiedCode.includes('function createGameObject');
  const hasAddObjectToParentMethod = unifiedCode.includes('function addObjectToParent');
  const hasSceneModifications = unifiedCode.includes('.prototype.start');
  
  console.log('- 包含findObjectByScenePath方法:', hasFindObjectMethod ? '✅' : '❌');
  console.log('- 包含createGameObject方法:', hasCreateGameObjectMethod ? '✅' : '❌');
  console.log('- 包含addObjectToParent方法:', hasAddObjectToParentMethod ? '✅' : '❌');
  console.log('- 包含场景修改代码:', hasSceneModifications ? '✅' : '❌');
  
  // 测试4: BackendService集成
  console.log('\n4. 测试BackendService集成...');
  
  // 验证BackendService方法存在
  console.log('- saveCode方法:', typeof BackendService.saveCode);
  console.log('- saveOperationRecord方法:', typeof BackendService.saveOperationRecord);
  console.log('- loadOperationRecord方法:', typeof BackendService.loadOperationRecord);
  console.log('- saveAndRefresh方法:', typeof BackendService.saveAndRefresh);
  console.log('- saveMultipleScenes方法:', typeof BackendService.saveMultipleScenes);
  
  // 测试5: 按场景分组生成代码
  console.log('\n5. 测试按场景分组生成代码...');
  const sceneCodeMap = manager.generateCodeByScene();
  console.log('- 场景数量:', sceneCodeMap.size);
  
  for (const [sceneName, sceneCode] of sceneCodeMap) {
    console.log(`- 场景 ${sceneName} 代码长度:`, sceneCode.length);
    
    // 验证场景代码结构
    const hasSceneStart = sceneCode.includes(`${sceneName}.prototype.start`);
    const hasOriginalCall = sceneCode.includes(`original${sceneName}Start.apply`);
    console.log(`  - ${sceneName} 包含prototype.start:`, hasSceneStart ? '✅' : '❌');
    console.log(`  - ${sceneName} 包含原始方法调用:`, hasOriginalCall ? '✅' : '❌');
  }
  
  // 测试6: 复杂属性处理
  console.log('\n6. 测试复杂属性处理...');
  const complexObj = createOperation(['Scene_Test', '5'], 'Sprite');
  
  // 设置各种类型的属性
  complexObj.setProperty('_bitmap.fontSize', 24);
  complexObj.setProperty('_bitmap.textColor', '#ffffff');
  complexObj.setProperty('_bitmap.outlineWidth', 2);
  complexObj.setProperty('colorTone', [255, 128, 0, 64]);
  complexObj.setProperty('blendColor', [0, 255, 128, 32]);
  complexObj.setProperty('filters', [{ type: 'blur', strength: 2 }]);
  
  const complexCode = complexObj.generateCode();
  console.log('- 复杂属性代码长度:', complexCode.length);
  
  // 验证特殊属性处理
  const hasColorToneMethod = complexCode.includes('setColorTone');
  const hasBlendColorMethod = complexCode.includes('setBlendColor');
  const hasBitmapProperty = complexCode.includes('_bitmap.fontSize');
  
  console.log('- 包含setColorTone方法:', hasColorToneMethod ? '✅' : '❌');
  console.log('- 包含setBlendColor方法:', hasBlendColorMethod ? '✅' : '❌');
  console.log('- 包含bitmap属性设置:', hasBitmapProperty ? '✅' : '❌');
  
  // 测试7: 操作统计
  console.log('\n7. 测试操作统计...');
  const stats = manager.getStatistics();
  console.log('- 统计信息:', stats);
  
  // 测试8: 清除操作
  console.log('\n8. 测试清除操作...');
  const beforeClearCount = manager.size();
  console.log('- 清除前操作数量:', beforeClearCount);
  
  // 清除单个操作
  const removed = manager.removeOperation(['Scene_Test', '5'], 'Sprite');
  console.log('- 删除操作成功:', removed);
  console.log('- 删除后操作数量:', manager.size());
  
  // 测试9: Map存储验证
  console.log('\n9. 测试Map存储验证...');
  const mapTestObj = new BaseTempObj(['Test', '1'], 'TestClass');
  
  // 设置null值应该删除属性
  mapTestObj.setProperty('testProp', 'value');
  console.log('- 设置值后是否修改:', mapTestObj.isPropertyModified('testProp'));
  
  mapTestObj.setProperty('testProp', null);
  console.log('- 设置null后是否修改:', mapTestObj.isPropertyModified('testProp'));
  
  // 设置undefined值应该删除属性
  mapTestObj.setProperty('testProp2', 'value2');
  mapTestObj.setProperty('testProp2', undefined);
  console.log('- 设置undefined后是否修改:', mapTestObj.isPropertyModified('testProp2'));
  
  console.log('\n✅ 最终架构测试完成！');
  
  // 总结
  console.log('\n📋 测试总结:');
  console.log('✅ BaseTempObj使用Map存储字段和值对应关系');
  console.log('✅ 删除了BaseTempObjData接口');
  console.log('✅ OperationManager生成统一合成代码');
  console.log('✅ 包含完整的对象查找和创建方法面板代码');
  console.log('✅ BackendService先调用OperationManager方法再调用后端API');
  console.log('✅ 支持复杂属性类型处理');
  console.log('✅ 序列化和反序列化正常工作');
  console.log('✅ 按场景分组生成代码功能完整');
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testFinalArchitecture();
}
