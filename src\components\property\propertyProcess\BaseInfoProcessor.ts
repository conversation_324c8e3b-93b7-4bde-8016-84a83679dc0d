/**
 * 基础信息组件处理器
 * 处理基础属性变化并应用到对象上
 */

export interface BaseInfoProperties {
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  scaleX?: number;
  scaleY?: number;
  rotation?: number;
  alpha?: number;
  visible?: boolean;
  zIndex?: number;
  name?: string;
  'anchor.x'?: number;
  'anchor.y'?: number;
  'skew.x'?: number;
  'skew.y'?: number;
  'border.width'?: number;
  'border.color'?: string;
  tint?: string;
  color?: string;
}

export class BaseInfoProcessor {
  /**
   * 应用基础属性到对象
   */
  static applyProperty(
    object: any,
    propertyName: keyof BaseInfoProperties,
    value: any
  ): void {
    if (!object) {
      console.warn('BaseInfoProcessor: 对象为空');
      return;
    }

    try {
      // 处理特殊属性
      if (this.handleSpecialProperty(object, propertyName, value)) {
        console.log(`BaseInfoProcessor: 已应用特殊属性 ${propertyName} = ${value} 到对象 ${object.constructor?.name}`);
        return;
      }

      // 处理嵌套属性（如 anchor.x, skew.y 等）
      if (propertyName.includes('.')) {
        this.setNestedProperty(object, propertyName, value);
        console.log(`BaseInfoProcessor: 已应用嵌套属性 ${propertyName} = ${value} 到对象 ${object.constructor?.name}`);
        return;
      }

      // 直接设置对象属性（用于实时预览）
      if (propertyName in object) {
        object[propertyName] = value;
      }

      console.log(`BaseInfoProcessor: 已应用属性 ${propertyName} = ${value} 到对象 ${object.constructor?.name}`);
    } catch (error) {
      console.error(`BaseInfoProcessor: 应用属性 ${propertyName} 失败:`, error);
    }
  }

  /**
   * 批量应用基础属性
   */
  static applyProperties(
    object: any,
    properties: Partial<BaseInfoProperties>
  ): void {
    if (!object) {
      console.warn('BaseInfoProcessor: 对象为空');
      return;
    }

    for (const [propertyName, value] of Object.entries(properties)) {
      if (value !== undefined && value !== null) {
        this.applyProperty(object, propertyName as keyof BaseInfoProperties, value);
      }
    }
  }

  /**
   * 验证属性值
   */
  static validateProperty(
    propertyName: keyof BaseInfoProperties,
    value: any
  ): boolean {
    switch (propertyName) {
      case 'x':
      case 'y':
      case 'width':
      case 'height':
      case 'scaleX':
      case 'scaleY':
      case 'rotation':
      case 'zIndex':
        return typeof value === 'number' && !isNaN(value);

      case 'alpha':
        return typeof value === 'number' && value >= 0 && value <= 1;

      case 'visible':
        return typeof value === 'boolean';

      case 'name':
        return typeof value === 'string';

      default:
        return true;
    }
  }

  /**
   * 获取属性的默认值
   */
  static getDefaultValue(propertyName: keyof BaseInfoProperties): any {
    switch (propertyName) {
      case 'x':
      case 'y':
        return 0;
      case 'width':
      case 'height':
        return 100;
      case 'scaleX':
      case 'scaleY':
        return 1;
      case 'rotation':
      case 'zIndex':
        return 0;
      case 'alpha':
        return 1;
      case 'visible':
        return true;
      case 'name':
        return '';
      default:
        return null;
    }
  }

  /**
   * 设置嵌套属性
   */
  static setNestedProperty(object: any, propertyPath: string, value: any): void {
    const parts = propertyPath.split('.');
    let current = object;

    // 遍历到最后一个属性的父对象
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];

      // 如果属性不存在，创建一个空对象
      if (!(part in current) || current[part] === null || current[part] === undefined) {
        current[part] = {};
      }

      current = current[part];
    }

    // 设置最后一个属性
    const lastPart = parts[parts.length - 1];
    current[lastPart] = value;
  }

  /**
   * 处理特殊属性设置
   */
  static handleSpecialProperty(
    object: any,
    propertyName: keyof BaseInfoProperties,
    value: any
  ): boolean {
    // 处理 anchor 属性
    if (propertyName === 'anchor.x' || propertyName === 'anchor.y') {
      if (!object.anchor) {
        object.anchor = { x: 0, y: 0 };
      }
      if (propertyName === 'anchor.x') {
        object.anchor.x = value;
      } else {
        object.anchor.y = value;
      }
      return true;
    }

    // 处理 skew 属性
    if (propertyName === 'skew.x' || propertyName === 'skew.y') {
      if (!object.skew) {
        object.skew = { x: 0, y: 0 };
      }
      if (propertyName === 'skew.x') {
        object.skew.x = value;
      } else {
        object.skew.y = value;
      }
      return true;
    }

    // 处理 border 属性
    if (propertyName === 'border.width' || propertyName === 'border.color') {
      if (!object.border) {
        object.border = { width: 0, color: '#000000' };
      }
      if (propertyName === 'border.width') {
        object.border.width = value;
      } else {
        object.border.color = value;
      }
      return true;
    }

    // 处理scale属性的特殊情况
    if (propertyName === 'scaleX' || propertyName === 'scaleY') {
      if (object.scale) {
        if (propertyName === 'scaleX') {
          object.scale.x = value;
        } else {
          object.scale.y = value;
        }
        return true;
      }
    }

    // 处理rotation属性（可能需要角度转弧度）
    if (propertyName === 'rotation') {
      // 如果对象期望弧度值，这里可以进行转换
      object.rotation = value;
      return true;
    }

    // 处理颜色属性
    if (propertyName === 'tint' || propertyName === 'color') {
      object.tint = value;
      return true;
    }

    return false;
  }
}
