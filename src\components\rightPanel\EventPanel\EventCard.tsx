import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  CardHeader,
  CardContent,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Typography,
  Box,
  Paper,
  Button,
  Snackbar
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import CodeIcon from '@mui/icons-material/Code';
import EditIcon from '@mui/icons-material/Edit';
import DroppableBase from '../../drag/DroppableBase';
import LightCodeEditor from '../../common/LightCodeEditor';
import useProjectStore from '../../../store/Store';
import { setObjectMethod } from '../../property/propertyProcess/objectMethod';

// 拖拽事件项接口
interface DroppedEvent {
  id: string;           // 事件唯一ID
  name: string;         // 事件名称
  type?: string;        // 事件类型（如'cutSceneEvent', 'animation', 'sound'等）
  description?: string; // 事件描述
  methodCode: string;   // 事件代码
  color?: string;       // 事件颜色标识
  metadata?: {          // 额外元数据
    [key: string]: any; // 自定义元数据
  };
}

interface EventCardProps {
  eventType: string;     // 事件类型，如 'onClick', 'onPress' 等
  initialCode?: string;  // 初始代码，从对象上获取的事件方法字符串
  onDelete?: (eventType: string) => void; // 删除事件卡片的回调函数
}

const EventCard: React.FC<EventCardProps> = ({
  eventType,
  initialCode,
  onDelete
}) => {
  // 从全局状态获取选中对象
  const selectedObjectsState = useProjectStore(state => state.selectedObjects);
  const selectedObject = selectedObjectsState.objects.length > 0 ? selectedObjectsState.objects[0] : null;
  // 内部状态管理 - 默认启用事件
  // const [enabled, setEnabled] = useState(true); // 事件是否启用
  // const [localCode, setLocalCode] = useState(''); // 本地事件代码

  // 存储拖拽过来的事件列表
  const [droppedEvents, setDroppedEvents] = useState<DroppedEvent[]>([]);
  // 存储编辑器中的代码
  const [editorCode, setEditorCode] = useState('');
  // 代码编辑器对话框状态
  const [editorDialogOpen, setEditorDialogOpen] = useState(false);

  // 消息提示状态
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // 处理拖拽悬停（仅用于视觉反馈）
  const handleDragOver = (_data: any) => {
    // 仅用于视觉反馈，不执行任何操作
    // 不要在这里添加日志，会产生大量输出
  };

  // 处理拖拽放置
  const handleDrop = (data: any) => {
    console.log('EventCard: 拖拽放置被触发:', data);

    if (data && data.type === 'cutSceneEvent' && data.scene) {
      const scene = data.scene;

      // 创建新的拖拽事件项
      const newEvent: DroppedEvent = {
        id: `${scene.id}_${Date.now()}`, // 创建唯一ID
        name: scene.displayName,
        type: 'cutSceneEvent',           // 添加事件类型
        description: `切换到场景 ${scene.name}`, // 添加描述
        methodCode: scene.methodCode,
        color: scene.color,
        metadata: {                      // 添加元数据
          sceneId: scene.id,
          sceneName: scene.name,
          timestamp: new Date().toISOString()
        }
      };

      console.log('EventCard: 添加新事件:', newEvent);

      // 如果是cutSceneEvent类型，检查是否已存在同类型事件
      setDroppedEvents(prev => {
        console.log('EventCard: 当前事件列表:', prev);

        // 检查是否已经存在cutSceneEvent类型的事件
        const existingCutSceneEventIndex = prev.findIndex(event =>
          event.id.includes(scene.id) || // 如果ID包含相同的场景ID
          (data.type === 'cutSceneEvent' && event.id.includes('_')) // 或者是任何cutSceneEvent类型
        );

        let newList;

        if (existingCutSceneEventIndex !== -1) {
          // 如果已存在，替换它
          console.log('EventCard: 替换现有的cutSceneEvent事件');
          newList = [...prev];
          newList[existingCutSceneEventIndex] = newEvent;
        } else {
          // 如果不存在，添加到列表
          newList = [...prev, newEvent];
        }

        console.log('EventCard: 更新后事件列表:', newList);
        return newList;
      });
    } else {
      console.log('EventCard: 无效的拖拽数据:', data);
    }
  };

  // 删除事件项
  const handleDeleteEvent = (eventId: string) => {
    setDroppedEvents(prev => prev.filter(event => event.id !== eventId));
  };

  // 打开代码编辑器对话框
  const handleOpenEditor = () => {
    setEditorDialogOpen(true);
  };

  // 关闭代码编辑器对话框
  const handleCloseEditor = () => {
    // 先移除焦点，避免aria-hidden错误
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }

    // 使用setTimeout延迟执行，确保DOM更新完成
    setTimeout(() => {
      setEditorDialogOpen(false);
    }, 0);
  };

  // 保存编辑器中的代码
  const handleSaveCode = (newCode: string) => {
    // 先移除焦点，避免aria-hidden错误
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }

    // 使用setTimeout延迟执行，确保DOM更新完成
    setTimeout(() => {
      console.log("保存编辑器中的代码", newCode);

      // 检查代码是否已经是函数声明，如果是则提取函数体
      const functionBodyMatch = newCode.match(/function\s*\([^)]*\)\s*{([\s\S]*)}$/);
      if (functionBodyMatch) {
        // 如果是函数声明，提取函数体内容
        const functionBody = functionBodyMatch[1].trim();
        console.log("提取函数体内容:", functionBody);
        setEditorCode(functionBody);
      } else {
        // 如果不是函数声明，直接使用原始代码
        setEditorCode(newCode);
      }
    }, 0);
  };

  // 使用 useRef 来跟踪上一次的值，避免无限循环
  const prevDroppedEventsRef = useRef<DroppedEvent[]>([]);
  const prevEditorCodeRef = useRef<string>('');
  const isFirstRenderRef = useRef<boolean>(true);

  // 解析初始代码
  useEffect(() => {
    if (!initialCode || !selectedObject) return;

    console.log(`解析初始代码: ${eventType}`, initialCode);

    try {
      // 检查代码是否包含事件标记
      const hasEventMarkers = initialCode.includes('EVENT_START') ||
        initialCode.includes('EDITOR_CODE_START') ||
        initialCode.includes('// 执行 ') ||
        initialCode.includes('// 执行自定义代码');

      if (hasEventMarkers) {
        // 解析拖拽事件
        const droppedEventsRegex = /\/\*\* EVENT_START:(.*?)\*\/([\s\S]*?)\/\*\* EVENT_END \*\//g;
        const newDroppedEvents: DroppedEvent[] = [];
        let match;

        while ((match = droppedEventsRegex.exec(initialCode)) !== null) {
          try {
            // 解析事件元数据 (JSON格式)
            const eventMetadata = JSON.parse(match[1]);
            const eventCode = match[2].trim();

            newDroppedEvents.push({
              id: eventMetadata.id || `parsed_${eventMetadata.name}_${Date.now()}`,
              name: eventMetadata.name,
              type: eventMetadata.type || 'unknown',
              description: eventMetadata.description,
              methodCode: eventCode,
              color: eventMetadata.color || '#1976d2',
              metadata: eventMetadata.metadata
            });
          } catch (parseError) {
            console.error('解析事件元数据失败:', parseError);
          }
        }

        // 如果没有找到新格式的事件，尝试解析旧格式
        if (newDroppedEvents.length === 0) {
          const oldEventsRegex = /\/\/ 执行 (.*?) 事件\n([\s\S]*?)(?=\n\n|$)/g;

          while ((match = oldEventsRegex.exec(initialCode)) !== null) {
            const eventName = match[1];
            const eventCode = match[2];

            newDroppedEvents.push({
              id: `legacy_${eventName}_${Date.now()}`,
              name: eventName,
              type: 'legacy',
              description: '从旧格式转换的事件',
              methodCode: eventCode,
              color: '#1976d2'
            });
          }
        }

        // 解析编辑器代码
        const editorCodeRegex = /\/\*\* EDITOR_CODE_START \*\/([\s\S]*?)\/\*\* EDITOR_CODE_END \*\//;
        const editorMatch = editorCodeRegex.exec(initialCode);

        if (editorMatch) {
          setEditorCode(editorMatch[1].trim());
        } else {
          // 尝试使用旧格式解析
          const oldEditorCodeRegex = /\/\/ 执行自定义代码(?:.*?)\n([\s\S]*?)(?=$)/;
          const oldEditorMatch = oldEditorCodeRegex.exec(initialCode);

          if (oldEditorMatch) {
            setEditorCode(oldEditorMatch[1].trim());
          }
        }

        // 更新拖拽事件列表
        if (newDroppedEvents.length > 0) {
          setDroppedEvents(newDroppedEvents);
        }
      } else {
        // 如果没有找到事件标记，但有代码，则将整个代码视为编辑器代码
        if (initialCode.trim()) {
          // 尝试提取函数体内容，去除函数声明包装
          const functionBodyMatch = initialCode.match(/function\s*\([^)]*\)\s*{([\s\S]*)}$/);
          if (functionBodyMatch) {
            // 如果是函数声明，提取函数体内容
            const functionBody = functionBodyMatch[1].trim();
            setEditorCode(functionBody);
          } else {
            // 如果不是函数声明，直接使用原始代码
            setEditorCode(initialCode.trim());
          }
        } else {
          // 如果没有代码或是空函数，则设置默认代码
          const defaultCode = `console.log("${eventType} 事件被触发了", this, event);`;
          setEditorCode(defaultCode);

          // 创建一个带有格式化标记的默认事件处理函数
          const formattedCode = `// ==================== 事件代码开始 ====================\n\n` +
            `// ========== 编辑器代码区块开始 ==========\n` +
            `/** EDITOR_CODE_START */\n` +
            `${defaultCode}\n` +
            `/** EDITOR_CODE_END */\n` +
            `// ========== 编辑器代码区块结束 ==========\n\n` +
            `// ==================== 事件代码结束 ====================\n`;

          // 设置对象方法
          setObjectMethod(selectedObject, eventType, formattedCode);
        }
      }
    } catch (error) {
      console.error(`解析初始代码时出错:`, error);
    }
  }, [initialCode, selectedObject, eventType]);

  useEffect(() => {
    // 初始化时，如果对象上已经有事件代码，将其设置到编辑器中
    if (isFirstRenderRef.current) {
      isFirstRenderRef.current = false;
      prevDroppedEventsRef.current = droppedEvents;
      prevEditorCodeRef.current = editorCode;

      if (selectedObject) {
        console.log('首次渲染，初始化事件代码');
        combineAndUpdateCode();
      }
      return;
    }

    // 检查 droppedEvents 是否真的变化了
    const droppedEventsChanged = JSON.stringify(prevDroppedEventsRef.current) !== JSON.stringify(droppedEvents);
    // 检查 editorCode 是否真的变化了
    const editorCodeChanged = prevEditorCodeRef.current !== editorCode;

    // 更新引用值
    prevDroppedEventsRef.current = droppedEvents;
    prevEditorCodeRef.current = editorCode;

    // 只有当值真的变化时才执行组合代码
    if ((droppedEventsChanged || editorCodeChanged) && selectedObject) {
      console.log('droppedEvents 或 editorCode 已变化，更新组合代码');
      combineAndUpdateCode();
    }
  }, [droppedEvents, editorCode, selectedObject]);

  // 组合并更新事件代码
  const combineAndUpdateCode = () => {
    // 如果没有对象或事件未启用，不执行任何操作
    if (!selectedObject) {
      console.log('没有对象或事件未启用，不执行更新');
      return;
    }

    try {
      // 组合所有代码：先执行列表中的事件代码，再执行编辑器中的代码
      let combinedCode = '';

      // 添加区块开始标记
      combinedCode += `// ==================== 事件代码开始 ====================\n\n`;

      // 添加拖拽事件区块
      if (droppedEvents.length > 0) {
        combinedCode += `// ========== 拖拽事件区块开始 ==========\n`;

        droppedEvents.forEach(event => {
          // 为每个事件添加JSON元数据标记
          const eventMetadata = {
            id: event.id,
            name: event.name,
            type: event.type || 'unknown',
            description: event.description || `${event.name} 事件`,
            color: event.color || '#1976d2',
            timestamp: new Date().toISOString()
          };

          combinedCode += `/** EVENT_START:${JSON.stringify(eventMetadata)}*/\n`;
          combinedCode += `${event.methodCode}\n`;
          combinedCode += `/** EVENT_END */\n\n`;
        });

        combinedCode += `// ========== 拖拽事件区块结束 ==========\n\n`;
      }

      // 添加编辑器代码区块
      let processedEditorCode = editorCode.trim();
      console.log("editorCode", editorCode);

      combinedCode += `// ========== 编辑器代码区块开始 ==========\n`;

      // 如果编辑器代码不为空，添加到组合代码中
      if (processedEditorCode) {
        combinedCode += `/** EDITOR_CODE_START */\n`;
        combinedCode += `${processedEditorCode}\n`;
        combinedCode += `/** EDITOR_CODE_END */\n`;
      } else {
        // 如果没有编辑器代码，添加默认函数表达式
        combinedCode += `/** EDITOR_CODE_START */\n`;
        combinedCode += `console.log("${eventType} 事件被触发了", this, event);\n`;
        combinedCode += `/** EDITOR_CODE_END */\n`;
      }

      combinedCode += `// ========== 编辑器代码区块结束 ==========\n\n`;

      // 添加区块结束标记
      combinedCode += `// ==================== 事件代码结束 ====================\n`;

      console.log(`更新 ${eventType} 事件代码:`, combinedCode);

      // 设置对象方法
      setObjectMethod(selectedObject, eventType, combinedCode);

      // 显示成功消息
      setSnackbarMessage(`${eventType} 事件代码已更新`);
      setSnackbarOpen(true);
    } catch (error) {
      console.error(`组合 ${eventType} 事件代码时出错:`, error);

      // 显示错误消息
      setSnackbarMessage(`更新事件代码失败: ${(error as Error).message || '未知错误'}`);
      setSnackbarOpen(true);
    }
  };


  // 处理删除事件卡片
  const handleDeleteEventCard = () => {
    try {
      // 禁用事件，设置为空函数
      if (selectedObject) {
        // 使用 setObjectMethod 设置一个空函数
        const emptyFunctionCode = 'function() {}';
        setObjectMethod(selectedObject, eventType, emptyFunctionCode);

        // 同时直接设置属性，确保两种方式都尝试
        selectedObject[eventType] = function () { };

        console.log(`已将 ${eventType} 事件重置为空函数`);
      }

      // 清空编辑器代码和拖拽事件
      setEditorCode('');
      setDroppedEvents([]);

      // 显示操作提示
      setSnackbarMessage(`${eventType} 事件已删除`);
      setSnackbarOpen(true);

      // 通知EventPanel组件删除此事件卡片
      if (onDelete) {
        console.log(`通知父组件删除 ${eventType} 事件卡片`);
        onDelete(eventType);
      } else {
        console.log(`没有提供 onDelete 回调，无法通知父组件删除事件卡片`);
      }

      console.log(`事件 ${eventType} 已删除`);
    } catch (error) {
      console.error('删除事件卡片时出错:', error);

      // 显示错误提示
      setSnackbarMessage(`删除事件失败: ${(error as Error).message || '未知错误'}`);
      setSnackbarOpen(true);
    }
  };

  // 当启用状态变化时，触发一次代码更新
  // useEffect(() => {
  //   if (enabled) {
  //     console.log('事件已启用，触发一次代码更新', enabled);
  //     // 使用setTimeout避免无限循环
  //     const timer = setTimeout(() => {
  //       combineAndUpdateCode();
  //     }, 100);
  //     return () => clearTimeout(timer);
  //   }
  // }, [enabled]);


  return (
    <Card variant="outlined" sx={{ mb: 1 }}>
      <CardHeader
        title={
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="subtitle2">{eventType} 事件</Typography>
            <IconButton
              size="small"
              color="error"
              onClick={() => handleDeleteEventCard()}
              sx={{ ml: 1 }}
              aria-label="删除事件"
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Box>
        }
        sx={{ pb: 0 }}
      />


      {/* 拖拽区域和事件列表 */}
      <DroppableBase id={`events-${eventType}`} accepts={['event']} onDragOver={handleDragOver} onDrop={handleDrop}>
        <List dense sx={{ maxHeight: 150, overflow: 'auto', pt: 0 }}>
          {droppedEvents.length === 0 ? (
            <ListItem>
              <ListItemText
                primary={
                  <Typography variant="caption" color="text.secondary">
                    将事件拖拽到此处添加到执行列表
                  </Typography>
                }
              />
            </ListItem>
          ) : (
            droppedEvents.map(event => (
              <ListItem
                key={event.id}
                divider
                secondaryAction={
                  <IconButton
                    edge="end"
                    size="small"
                    onClick={() => handleDeleteEvent(event.id)}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                }
              >
                <Box
                  component="span"
                  sx={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    bgcolor: event.color || 'primary.main',
                    mr: 1,
                    display: 'inline-block'
                  }}
                />
                <ListItemText
                  primary={
                    <Typography variant="body2">{event.name}</Typography>
                  }
                />
              </ListItem>
            ))
          )}
        </List>
      </DroppableBase>

      {/* 代码编辑器 */}
      <CardContent sx={{ pt: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="subtitle2">
            <CodeIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
            事件代码
          </Typography>
          <Button
            size="small"
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={handleOpenEditor}
          >
            编辑代码
          </Button>
        </Box>
        <Paper
          variant="outlined"
          sx={{
            p: 1,
            maxHeight: 200,
            overflow: 'auto',
            bgcolor: '#f5f5f5',
            fontFamily: 'monospace',
            fontSize: '0.75rem',
            whiteSpace: 'pre-wrap'
          }}
        >
          {editorCode}
        </Paper>
      </CardContent>


      {/* 代码编辑器对话框 */}
      <LightCodeEditor
        open={editorDialogOpen}
        title="编辑事件代码"
        code={editorCode}
        language="javascript"
        onClose={handleCloseEditor}
        onSave={handleSaveCode}
      />

      {/* 消息提示 */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />
    </Card>
  );
};

export default EventCard;
