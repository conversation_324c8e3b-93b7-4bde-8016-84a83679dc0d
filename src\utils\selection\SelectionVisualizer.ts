/**
 * SelectionVisualizer.ts
 * 处理选中对象的可视化显示，包括包围盒、锚点和顶点
 * 支持对象拖拽功能
 */

import useProjectStore from '../../store/Store';
import { setObjectProperty } from '../../services';

// 确保PIXI类型可用
declare global {
  interface Window {
    PIXI: any;
  }
}

// 存储创建的图形对象，方便后续清理
let visualElements: any[] = [];

// 拖拽状态
let isDragging = false;
let dragTarget: any = null;
let dragStartX = 0;
let dragStartY = 0;
let objectStartX = 0;
let objectStartY = 0;

// 颜色配置
const COLORS = {
  BOUNDING_BOX: 0xff0000, // 红色
  ANCHOR: 0x0000ff,       // 蓝色
  VERTEX: 0x00ff00,       // 绿色
};

/**
 * 初始化选中对象可视化
 * 设置监听器，当选中对象变化时更新可视化
 * 添加拖拽功能
 */
export function initSelectionVisualizer(): void {
  // 监听选中对象变化
  useProjectStore.subscribe((state) => {
    const { selectedObjects } = state;

    // 当选中对象变化时，更新可视化
    if (selectedObjects.type === 'object' && selectedObjects.objects.length > 0) {
      showSelectionVisuals(selectedObjects.objects);
    } else {
      clearSelectionVisuals();
    }
  });

  // 添加鼠标事件监听
  setupDragEvents();

  console.log('SelectionVisualizer initialized with drag support');
}

/**
 * 设置拖拽相关的事件监听
 */
function setupDragEvents(): void {
  try {
    // 获取canvas元素 - 尝试多种选择器
    let canvas: HTMLElement | null = document.querySelector('canvas');

    // 如果没有找到canvas，尝试查找id为gameCanvas的元素
    if (!canvas) {
      canvas = document.getElementById('gameCanvas');
    }

    // 如果仍然没有找到，尝试查找class包含canvas的元素
    if (!canvas) {
      const canvases = document.getElementsByClassName('canvas');
      if (canvases.length > 0) {
        canvas = canvases[0] as HTMLElement;
      }
    }

    // 如果仍然没有找到，尝试查找所有canvas元素
    if (!canvas) {
      const allCanvases = document.getElementsByTagName('canvas');
      if (allCanvases.length > 0) {
        canvas = allCanvases[0] as HTMLElement;
      }
    }

    if (!canvas) {
      console.warn('Canvas element not found, trying to add events to document');
      // 如果找不到canvas，将事件添加到document
      document.addEventListener('mousedown', handleMouseDown);
    } else {
      console.log('Canvas found, adding events:', canvas);
      // 添加鼠标按下事件
      canvas.addEventListener('mousedown', handleMouseDown);
    }

    // 添加鼠标移动事件（全局）
    document.addEventListener('mousemove', handleMouseMove);

    // 添加鼠标释放事件（全局）
    document.addEventListener('mouseup', handleMouseUp);

    console.log('Drag events setup completed');
  } catch (error) {
    console.error('Error setting up drag events:', error);
  }
}

/**
 * 处理鼠标按下事件
 */
function handleMouseDown(event: MouseEvent): void {
  try {
    console.log('Mouse down event detected:', event.clientX, event.clientY);

    // 获取当前选中的对象
    const selectedObjects = useProjectStore.getState().selectedObjects;

    console.log('Selected objects:', selectedObjects);

    if (selectedObjects.type !== 'object' || selectedObjects.objects.length === 0) {
      console.log('No objects selected or selection type is not "object"');
      return;
    }

    // 获取鼠标位置
    const mouseX = event.clientX;
    const mouseY = event.clientY;

    // 获取canvas元素
    const canvas = document.querySelector('canvas');
    if (canvas) {
      console.log('Canvas found:', canvas);
      console.log('Canvas dimensions:', canvas.width, canvas.height);
      console.log('Canvas bounding rect:', canvas.getBoundingClientRect());
    } else {
      console.log('Canvas not found');
    }

    console.log('Checking if mouse position is within bounds of selected objects');

    // 检查是否点击在选中对象的包围盒内
    for (const obj of selectedObjects.objects) {
      console.log('Checking object:', obj);

      // 打印对象的位置和尺寸信息
      console.log('Object position:', obj.x, obj.y);
      console.log('Object dimensions:', (obj as any).width, (obj as any).height);

      // 尝试获取对象的全局位置
      let globalPos = { x: 0, y: 0 };
      if (typeof obj.getGlobalPosition === 'function') {
        try {
          globalPos = obj.getGlobalPosition();
          console.log('Object global position:', globalPos);
        } catch (e) {
          console.log('Error getting global position');
        }
      }

      // 检查点击是否在对象边界内
      const result = isPointInObjectBounds(obj, mouseX, mouseY);
      console.log('Is point in bounds?', result);

      if (result) {
        // 开始拖拽
        isDragging = true;
        dragTarget = obj;
        dragStartX = mouseX;
        dragStartY = mouseY;
        objectStartX = obj.x || 0;
        objectStartY = obj.y || 0;

        console.log('Started dragging object:', obj);
        console.log('Drag start position:', dragStartX, dragStartY);
        console.log('Object start position:', objectStartX, objectStartY);

        // 如果对象有父对象，记录父对象信息
        if (obj.parent) {
          console.log('Object has parent:', obj.parent);
          console.log('Parent position:', obj.parent.x, obj.parent.y);
        }

        event.preventDefault();
        event.stopPropagation();
        break;
      }
    }

    // 如果没有找到可拖拽的对象，尝试直接拖拽第一个选中的对象
    if (!isDragging && selectedObjects.objects.length > 0) {
      console.log('No object found in bounds, trying to drag the first selected object directly');

      const obj = selectedObjects.objects[0];
      isDragging = true;
      dragTarget = obj;
      dragStartX = mouseX;
      dragStartY = mouseY;
      objectStartX = obj.x || 0;
      objectStartY = obj.y || 0;

      console.log('Started dragging object directly:', obj);
      console.log('Drag start position:', dragStartX, dragStartY);
      console.log('Object start position:', objectStartX, objectStartY);

      event.preventDefault();
      event.stopPropagation();
    }
  } catch (error) {
    console.error('Error in mouse down handler:', error);
  }
}

/**
 * 处理鼠标移动事件
 */
function handleMouseMove(event: MouseEvent): void {
  // 如果没有拖拽目标或者没有处于拖拽状态，直接返回
  if (!isDragging || !dragTarget) {
    return;
  }

  try {
    console.log('Mouse move event with dragging active');

    // 计算鼠标移动的距离
    const deltaX = event.clientX - dragStartX;
    const deltaY = event.clientY - dragStartY;

    console.log('Mouse delta:', deltaX, deltaY);
    console.log('Original object position:', objectStartX, objectStartY);

    // 更新对象位置
    const newX = objectStartX + deltaX;
    const newY = objectStartY + deltaY;

    console.log('Setting new position:', newX, newY);

    // 获取Store
    const store = useProjectStore.getState();
    const selectedObjects = store.selectedObjects;

    // 直接设置对象位置
    dragTarget.x = newX;
    dragTarget.y = newY;

    console.log('New object position after update:', dragTarget.x, dragTarget.y);

    // 如果对象有更新方法，调用它
    if (typeof dragTarget.update === 'function') {
      console.log('Calling object update method');
      dragTarget.update();
    }

    // 如果对象有position属性，也更新它
    if (dragTarget.position) {
      console.log('Updating position property');
      dragTarget.position.x = newX;
      dragTarget.position.y = newY;
    }

    // 更新Store中selectedObjects中的对象
    if (selectedObjects.type === 'object' && selectedObjects.objects.length > 0) {
      console.log('Updating objects in selectedObjects during drag');

      // 直接更新拖拽对象的属性
      dragTarget.x = newX;
      dragTarget.y = newY;

      // 如果对象有position属性，也更新它
      if (dragTarget.position) {
        dragTarget.position.x = newX;
        dragTarget.position.y = newY;
      }

      // 使用setSelectedObjectsProperty方法更新对象属性并刷新UI
      // 这个方法既能更新对象属性，又能触发UI刷新
      store.setSelectedObjectsProperty('x', newX);
      store.setSelectedObjectsProperty('y', newY);

      // 触发全局事件，通知其他组件对象已更新
      store.emit('objectPositionChanged', dragTarget, newX, newY);

      console.log('Updated selectedObjects using setSelectedObjectsPropte during drag');
    }

    // 更新可视化效果
    updateDraggedObjectVisual();

    // 阻止事件冒泡和默认行为
    event.preventDefault();
    event.stopPropagation();
  } catch (error) {
    console.error('Error in mouse move handler:', error);

    // 发生错误时重置拖拽状态
    isDragging = false;
    dragTarget = null;
  }
}

/**
 * 处理鼠标释放事件
 */
function handleMouseUp(_event: MouseEvent): void {
  if (!isDragging || !dragTarget) return;

  try {
    console.log('Finished dragging object:', dragTarget);

    // 更新Store中的对象位置
    updateObjectPositionInStore(dragTarget);

    // 重置拖拽状态
    isDragging = false;
    dragTarget = null;

    // 更新Store中的选中对象
    const selectedObjects = useProjectStore.getState().selectedObjects;
    if (selectedObjects.type === 'object' && selectedObjects.objects.length > 0) {
      // 触发更新，确保可视化效果正确
      showSelectionVisuals(selectedObjects.objects);
    }
  } catch (error) {
    console.error('Error in mouse up handler:', error);
  }
}

/**
 * 更新Store中的对象位置
 */
function updateObjectPositionInStore(object: any): void {
  try {
    // 获取Store
    const store = useProjectStore.getState();

    console.log('Updating object position in store:', object.x, object.y);

    // 使用setSelectedObjectsProperty方法更新对象属性并刷新UI
    // 这个方法既能更新对象属性，又能触发UI刷新
    store.setSelectedObjectsProperty('x', object.x);
    store.setSelectedObjectsProperty('y', object.y);

    console.log('Updated object position using setSelectedObjectsPropte');

    // 尝试记录属性修改到后端
    try {
      // 记录x坐标修改
      setObjectProperty(
        (object as any)._rpgEditorPath || ['Unknown'],
        object.constructor?.name || 'Unknown',
        'x',
        object.x
      );
      console.log('Position x recorded to backend');

      // 记录y坐标修改
      setObjectProperty(
        (object as any)._rpgEditorPath || ['Unknown'],
        object.constructor?.name || 'Unknown',
        'y',
        object.y
      );
      console.log('Position y recorded to backend');
    } catch (e) {
      console.warn('Failed to record position changes:', e);
    }
  } catch (error) {
    console.error('Error updating object position in store:', error);
  }
}

/**
 * 检查点是否在对象的包围盒内
 */
function isPointInObjectBounds(object: any, x: number, y: number): boolean {
  try {
    if (!object) {
      console.log('Object is null or undefined');
      return false;
    }

    // 获取对象的宽度和高度
    const width = object.width || 0;
    const height = object.height || 0;

    console.log('Object dimensions in isPointInObjectBounds:', width, height);

    if (width === 0 || height === 0) {
      console.log('Object has zero width or height');
      return false;
    }

    // 获取canvas元素
    const canvas = document.querySelector('canvas');
    if (!canvas) {
      console.log('Canvas not found, using document coordinates');
    }

    // 获取canvas的边界矩形
    const canvasRect = canvas ? canvas.getBoundingClientRect() : { left: 0, top: 0 };
    console.log('Canvas rect:', canvasRect);

    // 调整鼠标坐标，使其相对于canvas
    const adjustedX = x - (canvasRect.left || 0);
    const adjustedY = y - (canvasRect.top || 0);
    console.log('Adjusted mouse position:', adjustedX, adjustedY);

    // 获取对象的全局位置
    let globalPos = { x: 0, y: 0 };

    if (typeof object.getGlobalPosition === 'function') {
      try {
        globalPos = object.getGlobalPosition();
        console.log('Got global position from getGlobalPosition:', globalPos);
      } catch (e) {
        console.log('Error getting global position, using x/y properties');
        // 尝试使用对象的x和y属性
        globalPos = {
          x: typeof object.x === 'number' ? object.x : 0,
          y: typeof object.y === 'number' ? object.y : 0
        };
      }
    } else {
      console.log('getGlobalPosition not available, using x/y properties');
      // 尝试使用对象的x和y属性
      globalPos = {
        x: typeof object.x === 'number' ? object.x : 0,
        y: typeof object.y === 'number' ? object.y : 0
      };
    }

    // 获取锚点（如果存在）
    const anchorX = object.anchor ? (object.anchor.x || 0) : 0;
    const anchorY = object.anchor ? (object.anchor.y || 0) : 0;

    console.log('Anchor:', anchorX, anchorY);

    // 计算包围盒的边界
    const left = globalPos.x - anchorX * width;
    const top = globalPos.y - anchorY * height;
    const right = left + width;
    const bottom = top + height;

    console.log('Bounding box:', left, top, right, bottom);
    console.log('Original mouse position:', x, y);
    console.log('Adjusted mouse position:', adjustedX, adjustedY);

    // 检查点是否在包围盒内
    const result = adjustedX >= left && adjustedX <= right && adjustedY >= top && adjustedY <= bottom;
    console.log('Point in bounds result:', result);

    // 如果第一次检查失败，尝试使用原始坐标
    if (!result) {
      console.log('Trying with original coordinates');
      const originalResult = x >= left && x <= right && y >= top && y <= bottom;
      console.log('Original coordinates result:', originalResult);

      // 如果原始坐标检查成功，返回true
      if (originalResult) {
        return true;
      }

      // 尝试使用不同的坐标系
      console.log('Trying with different coordinate system');
      // 假设对象坐标是相对于canvas的，而鼠标坐标是相对于页面的
      const canvasX = x - (canvasRect.left || 0);
      const canvasY = y - (canvasRect.top || 0);

      // 尝试直接比较鼠标位置和对象尺寸
      const simpleResult = canvasX >= 0 && canvasX <= width && canvasY >= 0 && canvasY <= height;
      console.log('Simple bounds check result:', simpleResult);

      // 如果简单检查成功，返回true
      if (simpleResult) {
        return true;
      }
    }

    return result;
  } catch (error) {
    console.error('Error checking if point is in bounds:', error);
    return false;
  }
}

/**
 * 更新被拖拽对象的可视化效果
 */
function updateDraggedObjectVisual(): void {
  try {
    if (!dragTarget) {
      console.log('No drag target, skipping visual update');
      return;
    }

    console.log('Updating visuals for dragged object:', dragTarget);
    console.log('Current position:', dragTarget.x, dragTarget.y);

    // 清除当前的可视化效果
    clearSelectionVisuals();

    // 重新创建可视化效果
    const selectedObjects = useProjectStore.getState().selectedObjects;
    if (selectedObjects.type === 'object' && selectedObjects.objects.length > 0) {
      console.log('Showing selection visuals for objects:', selectedObjects.objects);
      showSelectionVisuals(selectedObjects.objects);
    } else {
      console.log('No selected objects in store, creating visual for drag target only');
      // 如果Store中没有选中对象，只为拖拽目标创建可视化效果
      createVisualForObject(dragTarget);
    }
  } catch (error) {
    console.error('Error updating dragged object visual:', error);
  }
}

/**
 * 显示选中对象的可视化效果
 * @param objects 选中的对象数组
 */
export function showSelectionVisuals(objects: any[]): void {
  // 先清除之前的可视化效果
  clearSelectionVisuals();

  // 为每个选中的对象创建可视化效果
  objects.forEach(object => {
    if (object && (object.visible === undefined || object.visible !== false)) {
      createVisualForObject(object);
    }
  });
}

/**
 * 清除所有可视化效果
 */
export function clearSelectionVisuals(): void {
  // 移除所有创建的图形对象
  visualElements.forEach(element => {
    try {
      if (element && element.parent && typeof element.parent.removeChild === 'function') {
        element.parent.removeChild(element);
      }
    } catch (error) {
      console.warn('Error removing visual element:', error);
    }
  });

  // 清空数组
  visualElements = [];
}

/**
 * 为单个对象创建可视化效果
 * @param object 需要可视化的对象
 */
function createVisualForObject(object: any): void {
  try {
    // 确保PIXI可用
    if (!window.PIXI) {
      console.error('PIXI is not available');
      return;
    }

    // 创建图形对象
    const graphics = new window.PIXI.Graphics();

    // 获取对象的宽度和高度
    const width = object.width || 0;
    const height = object.height || 0;

    if (width === 0 || height === 0) return; // 跳过没有尺寸的对象

    // 获取锚点（如果存在）
    const anchorX = object.anchor ? (object.anchor.x || 0) : 0;
    const anchorY = object.anchor ? (object.anchor.y || 0) : 0;

    // 绘制包围盒（红色）
    graphics.lineStyle(2, COLORS.BOUNDING_BOX, 1);
    graphics.drawRect(
      -anchorX * width,
      -anchorY * height,
      width,
      height
    );

    // 绘制锚点（蓝色）
    graphics.lineStyle(0);
    graphics.beginFill(COLORS.ANCHOR, 1);
    graphics.drawCircle(0, 0, 4);
    graphics.endFill();

    // 绘制四个顶点（绿色）
    graphics.lineStyle(0);
    graphics.beginFill(COLORS.VERTEX, 1);

    const corners = [
      [-anchorX * width, -anchorY * height],           // 左上
      [width - anchorX * width, -anchorY * height],    // 右上
      [width - anchorX * width, height - anchorY * height], // 右下
      [-anchorX * width, height - anchorY * height]    // 左下
    ];

    corners.forEach(([x, y]) => {
      graphics.drawCircle(x, y, 3);
    });

    graphics.endFill();

    // 将图形添加到对象
    if (typeof object.addChild === 'function') {
      object.addChild(graphics);

      // 确保图形在最上层
      if (typeof graphics.zIndex === 'number') {
        graphics.zIndex = 9999;
      }

      // 存储创建的图形对象，方便后续清理
      visualElements.push(graphics);
    } else {
      console.warn('Object does not have addChild method:', object);
    }
  } catch (error) {
    console.error('Error creating visual for object:', error);
  }
}



// 导出默认函数，用于初始化
export default initSelectionVisualizer;
