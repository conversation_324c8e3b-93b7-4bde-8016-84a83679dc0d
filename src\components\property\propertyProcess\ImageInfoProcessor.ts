/**
 * 图片信息组件处理器
 * 处理图片相关属性变化并应用到对象上
 */



export interface ImageInfoProperties {
  texture?: string;
  'bitmap._url'?: string;
  anchor?: { x: number; y: number };
  tint?: number;
  blendMode?: number;
  frame?: { x: number; y: number; width: number; height: number };
}

export class ImageInfoProcessor {
  /**
   * 应用图片属性到对象
   */
  static applyProperty(
    object: any,
    propertyName: keyof ImageInfoProperties,
    value: any
  ): void {
    if (!object) {
      console.warn('ImageInfoProcessor: 对象为空');
      return;
    }

    try {
      // 处理特殊属性
      if (this.handleSpecialProperty(object, propertyName, value)) {
        console.log(`ImageInfoProcessor: 已应用特殊属性 ${propertyName} 到对象 ${object.constructor?.name}`);
        return;
      }

      // 直接设置对象属性（用于实时预览）
      if (propertyName in object) {
        object[propertyName] = value;
      }

      console.log(`ImageInfoProcessor: 已应用属性 ${propertyName} = ${value} 到对象 ${object.constructor?.name}`);
    } catch (error) {
      console.error(`ImageInfoProcessor: 应用属性 ${propertyName} 失败:`, error);
    }
  }

  /**
   * 批量应用图片属性
   */
  static applyProperties(
    object: any,
    properties: Partial<ImageInfoProperties>
  ): void {
    if (!object) {
      console.warn('ImageInfoProcessor: 对象为空');
      return;
    }

    for (const [propertyName, value] of Object.entries(properties)) {
      if (value !== undefined && value !== null) {
        this.applyProperty(object, propertyName as keyof ImageInfoProperties, value);
      }
    }
  }

  /**
   * 处理特殊属性设置
   */
  static handleSpecialProperty(
    object: any,
    propertyName: keyof ImageInfoProperties,
    value: any
  ): boolean {
    switch (propertyName) {
      case 'texture':
      case 'bitmap._url':
        return this.handleTextureChange(object, value);

      case 'anchor':
        return this.handleAnchorChange(object, value);

      case 'frame':
        return this.handleFrameChange(object, value);

      default:
        return false;
    }
  }

  /**
   * 处理纹理变化
   */
  private static handleTextureChange(object: any, imagePath: string): boolean {
    try {
      if (typeof window !== 'undefined' && (window as any).ImageManager) {
        const ImageManager = (window as any).ImageManager;

        // 使用RPG Maker的ImageManager加载图片
        const newBitmap = ImageManager.loadBitmapFromUrl(imagePath);

        newBitmap.addLoadListener(function (bitmap: any) {
          if (object.bitmap) {
            object.bitmap = bitmap;
          } else if (object.texture) {
            // 对于PIXI Sprite，更新texture
            object.texture = bitmap._baseTexture ?
              new (window as any).PIXI.Texture(bitmap._baseTexture) :
              bitmap;
          }

          console.log('ImageInfoProcessor: 纹理加载完成:', imagePath);
        });

        return true;
      } else {
        console.warn('ImageInfoProcessor: ImageManager不可用');
        return false;
      }
    } catch (error) {
      console.error('ImageInfoProcessor: 处理纹理变化失败:', error);
      return false;
    }
  }

  /**
   * 处理锚点变化
   */
  private static handleAnchorChange(object: any, anchor: { x: number; y: number }): boolean {
    try {
      if (object.anchor) {
        object.anchor.x = anchor.x;
        object.anchor.y = anchor.y;
        return true;
      }
      return false;
    } catch (error) {
      console.error('ImageInfoProcessor: 处理锚点变化失败:', error);
      return false;
    }
  }

  /**
   * 处理帧变化
   */
  private static handleFrameChange(object: any, frame: { x: number; y: number; width: number; height: number }): boolean {
    try {
      if (object.texture && object.texture.frame) {
        object.texture.frame.x = frame.x;
        object.texture.frame.y = frame.y;
        object.texture.frame.width = frame.width;
        object.texture.frame.height = frame.height;
        object.texture.updateUvs();
        return true;
      }
      return false;
    } catch (error) {
      console.error('ImageInfoProcessor: 处理帧变化失败:', error);
      return false;
    }
  }

  /**
   * 验证属性值
   */
  static validateProperty(
    propertyName: keyof ImageInfoProperties,
    value: any
  ): boolean {
    switch (propertyName) {
      case 'texture':
      case 'bitmap._url':
        return typeof value === 'string' && value.length > 0;

      case 'tint':
      case 'blendMode':
        return typeof value === 'number';

      case 'anchor':
        return value &&
          typeof value.x === 'number' &&
          typeof value.y === 'number' &&
          value.x >= 0 && value.x <= 1 &&
          value.y >= 0 && value.y <= 1;

      case 'frame':
        return value &&
          typeof value.x === 'number' &&
          typeof value.y === 'number' &&
          typeof value.width === 'number' &&
          typeof value.height === 'number' &&
          value.width > 0 && value.height > 0;

      default:
        return true;
    }
  }

  /**
   * 获取属性的默认值
   */
  static getDefaultValue(propertyName: keyof ImageInfoProperties): any {
    switch (propertyName) {
      case 'texture':
      case 'bitmap._url':
        return '';
      case 'tint':
        return 0xFFFFFF;
      case 'blendMode':
        return 0;
      case 'anchor':
        return { x: 0, y: 0 };
      case 'frame':
        return { x: 0, y: 0, width: 100, height: 100 };
      default:
        return null;
    }
  }
}
