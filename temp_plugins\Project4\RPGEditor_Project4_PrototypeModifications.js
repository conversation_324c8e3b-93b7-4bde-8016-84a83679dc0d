(function() {
"use strict";
// ==================== RPG Editor 生成的修改代码 ====================
// 此代码由 RPG Editor 自动生成，请勿手动修改
// 生成时间: 2025/5/30 08:47:50



// ==================== 对象查找方法 ====================
    // 获取插件参数
    const parameters = PluginManager.parameters("RPGEditor_PrototypeModifications");
    const DEBUG = parameters["debug"] === "true";

    // 调试日志函数
    function log(message, ...args) {
        if (DEBUG) {
            console.log("[RPGEditor]", message, ...args);
        }
    }
    // 工具函数：根据场景路径查找对象
    function findObjectByScenePath(scenePath) {
        if (!scenePath || scenePath.length === 0) return null;

        let current = SceneManager._scene;
        if (!current) return null;

        // 检查第一个元素是否为创建操作标记
        const firstElement = scenePath[0];
        let isCreationOperation = firstElement === "+";

        // 确定实际的场景路径起始位置
        let startIndex = isCreationOperation ? 1 : 0;

        // 验证场景名称
        const expectedSceneName = scenePath[startIndex];
        const actualSceneName = current.constructor.name;
        if (actualSceneName !== expectedSceneName) {
            if (DEBUG) console.log(`[Scene Mismatch] Expected: ${expectedSceneName}, Actual: ${actualSceneName}`);
            return null;
        }

        // 确定遍历的结束位置
        let endIndex = scenePath.length;
        if (isCreationOperation) {
            // 创建操作：最后一个索引不查找，那是要创建的位置
            endIndex = scenePath.length - 1;
            if (DEBUG) console.log(`[Creation Operation] Finding parent object, skipping last index: ${scenePath[scenePath.length - 1]}`);
        }

        // 遍历路径
        for (let i = startIndex + 1; i < endIndex; i++) {
            const index = parseInt(scenePath[i]);
            if (isNaN(index) || !current.children || !current.children[index]) {
                if (DEBUG) console.log(`[Path Break] Index ${index} does not exist in ${current.constructor.name}`);
                return null;
            }
            current = current.children[index];
        }

        return current;
    }


    // 通用滤镜类：继承自PIXI.Filter，可以接受任意着色器和参数
    class UniversalFilter extends window.PIXI.Filter {
        constructor(vertexShader, fragmentShader, uniforms = {}) {
            super(vertexShader, fragmentShader, uniforms);

            // 存储滤镜类型信息
            this.filterType = 'universal';
            this.autoUpdate = false;
            this._lastTime = Date.now();
        }

        // 设置uniforms参数
        setUniforms(uniforms) {
            for (const [key, value] of Object.entries(uniforms)) {
                this.uniforms[key] = value;
            }
        }

        // 设置属性
        setProperties(properties) {
            for (const [key, value] of Object.entries(properties)) {
                if (key === 'enabled') {
                    this.enabled = value;
                } else if (key === 'autoUpdate') {
                    this.autoUpdate = value;
                } else if (this.uniforms && this.uniforms.hasOwnProperty(key)) {
                    this.uniforms[key] = value;
                } else {
                    this[key] = value;
                }
            }
        }

        // 应用滤镜前更新时间（如果需要）
        apply(filterManager, input, output, clear) {
            if (this.autoUpdate && this.uniforms.time !== undefined) {
                const now = Date.now();
                this.uniforms.time += (now - this._lastTime) * 0.001;
                this._lastTime = now;
            }
            super.apply(filterManager, input, output, clear);
        }
    }

    // 通用滤镜工厂：创建滤镜实例
    function createFilterInstance(filterType, params = {}) {
        if (DEBUG) log(`[Filter] Creating universal filter: ${filterType}`, params);

        try {
            const PIXI = window.PIXI;
            if (!PIXI) {
                console.error('[Filter] PIXI 对象不可用');
                return null;
            }

            let filter = null;

            // 处理PIXI内置滤镜
            if (filterType === 'blur' && PIXI.filters && PIXI.filters.BlurFilter) {
                filter = new PIXI.filters.BlurFilter();
            } else if (filterType === 'alpha' && PIXI.filters && PIXI.filters.AlphaFilter) {
                filter = new PIXI.filters.AlphaFilter();
            } else if (filterType === 'colorMatrix' && PIXI.filters && PIXI.filters.ColorMatrixFilter) {
                filter = new PIXI.filters.ColorMatrixFilter();
            } else if (filterType === 'noise' && PIXI.filters && PIXI.filters.NoiseFilter) {
                filter = new PIXI.filters.NoiseFilter();
            } else {
                // 处理自定义滤镜：使用通用滤镜类
                const shaderConfig = getFilterShaderConfig(filterType);
                if (shaderConfig) {
                    filter = new UniversalFilter(
                        shaderConfig.vertexShader,
                        shaderConfig.fragmentShader,
                        shaderConfig.defaultUniforms || {}
                    );
                    filter.filterType = filterType;

                    // 设置自动更新（如果需要时间参数）
                    if (shaderConfig.autoUpdate) {
                        filter.autoUpdate = true;
                    }
                }
            }

            if (!filter) {
                console.warn(`[Filter] 无法创建滤镜类型: ${filterType}`);
                return null;
            }

            // 设置参数
            if (filter.setProperties) {
                filter.setProperties(params);
            } else {
                // 对于PIXI内置滤镜，直接设置属性
                for (const [key, value] of Object.entries(params)) {
                    if (key === 'enabled') {
                        filter.enabled = value;
                    } else if (filter.uniforms && filter.uniforms.hasOwnProperty(key)) {
                        filter.uniforms[key] = value;
                    } else if (filter.hasOwnProperty(key)) {
                        filter[key] = value;
                    } else {
                        try {
                            filter[key] = value;
                        } catch (e) {
                            if (DEBUG) console.warn(`[Filter] 无法设置属性 ${key}:`, e);
                        }
                    }
                }
            }

            if (DEBUG) log(`[Filter] 成功创建并配置滤镜: ${filterType}`, filter);
            return filter;

        } catch (error) {
            console.error(`[Filter] 创建滤镜时出错: ${filterType}`, error);
            return null;
        }
    }

    // 获取滤镜着色器配置
    function getFilterShaderConfig(filterType) {
        const vertexShader = `
            attribute vec2 aVertexPosition;
            attribute vec2 aTextureCoord;
            uniform mat3 projectionMatrix;
            varying vec2 vTextureCoord;
            void main(void) {
                gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
                vTextureCoord = aTextureCoord;
            }
        `;

        const configs = {
            'smoke': {
                vertexShader,
                fragmentShader: `
                    varying vec2 vTextureCoord;
                    uniform sampler2D uSampler;
                    uniform float time;
                    uniform float intensity;

                    void main(void) {
                        vec2 uv = vTextureCoord;
                        float noise = fract(sin(dot(uv + time * 0.05, vec2(12.9898, 78.233))) * 43758.5453);
                        noise = noise * 0.5 - 0.25;
                        vec2 offset = vec2(noise * intensity * 0.02);
                        vec4 color = texture2D(uSampler, uv + offset);
                        float gray = (color.r + color.g + color.b) / 3.0;
                        vec4 smokeColor = mix(color, vec4(gray, gray, gray, color.a), intensity * 0.3);
                        gl_FragColor = smokeColor;
                    }
                `,
                defaultUniforms: { time: 0.0, intensity: 0.5 },
                autoUpdate: true
            },
            'fire': {
                vertexShader,
                fragmentShader: `
                    varying vec2 vTextureCoord;
                    uniform sampler2D uSampler;
                    uniform float time;
                    uniform float intensity;
                    uniform vec3 fireColor;

                    float random(vec2 st) {
                        return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
                    }

                    float noise(vec2 st) {
                        vec2 i = floor(st);
                        vec2 f = fract(st);
                        float a = random(i);
                        float b = random(i + vec2(1.0, 0.0));
                        float c = random(i + vec2(0.0, 1.0));
                        float d = random(i + vec2(1.0, 1.0));
                        vec2 u = f * f * (3.0 - 2.0 * f);
                        return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
                    }

                    void main(void) {
                        vec2 uv = vTextureCoord;
                        vec4 color = texture2D(uSampler, uv);
                        float n = noise(vec2(uv.x * 10.0, (uv.y * 10.0 - time * 2.0)));
                        float mask = 1.0 - uv.y;
                        mask = pow(mask, 1.0 + intensity * 2.0) * 2.0;
                        float fire = n * mask;
                        fire = smoothstep(0.1, 0.9, fire);
                        vec3 fireColorMix = mix(color.rgb, fireColor, fire * intensity);
                        gl_FragColor = vec4(fireColorMix, color.a);
                    }
                `,
                defaultUniforms: { time: 0.0, intensity: 0.5, fireColor: [1.0, 0.5, 0.0] },
                autoUpdate: true
            },
            'water': {
                vertexShader,
                fragmentShader: `
                    varying vec2 vTextureCoord;
                    uniform sampler2D uSampler;
                    uniform float time;
                    uniform float amplitude;
                    uniform float frequency;

                    void main(void) {
                        vec2 uv = vTextureCoord;
                        float x = uv.x * frequency + time;
                        float y = uv.y * frequency + time;
                        float sinX = sin(x) * amplitude * 0.01;
                        float sinY = sin(y) * amplitude * 0.01;
                        vec2 offset = vec2(sinX, sinY);
                        gl_FragColor = texture2D(uSampler, uv + offset);
                    }
                `,
                defaultUniforms: { time: 0.0, amplitude: 5.0, frequency: 10.0 },
                autoUpdate: true
            },
            'cloth': {
                vertexShader,
                fragmentShader: `
                    varying vec2 vTextureCoord;
                    uniform sampler2D uSampler;
                    uniform float time;
                    uniform float intensity;
                    uniform float waveFrequency;

                    void main(void) {
                        vec2 uv = vTextureCoord;
                        float wave = sin(uv.y * waveFrequency + time) * intensity * 0.01;
                        vec2 distortion = vec2(wave, 0.0);
                        float noise = fract(sin(dot(uv, vec2(12.9898, 78.233))) * 43758.5453);
                        distortion += vec2(noise * intensity * 0.002);
                        gl_FragColor = texture2D(uSampler, uv + distortion);
                    }
                `,
                defaultUniforms: { time: 0.0, intensity: 5.0, waveFrequency: 20.0 },
                autoUpdate: true
            },
            'glow': {
                vertexShader,
                fragmentShader: `
                    varying vec2 vTextureCoord;
                    uniform sampler2D uSampler;
                    uniform float intensity;
                    uniform vec3 glowColor;
                    uniform float time;

                    void main(void) {
                        vec4 color = texture2D(uSampler, vTextureCoord);
                        float glow = 0.0;
                        float blurSize = 0.01 * intensity;
                        for(float x = -4.0; x <= 4.0; x += 1.0) {
                            for(float y = -4.0; y <= 4.0; y += 1.0) {
                                vec2 offset = vec2(x, y) * blurSize;
                                glow += texture2D(uSampler, vTextureCoord + offset).a;
                            }
                        }
                        glow /= 81.0;
                        glow *= intensity;
                        float pulse = (sin(time) * 0.1 + 0.9) * intensity;
                        vec3 finalColor = mix(color.rgb, glowColor, glow * pulse);
                        gl_FragColor = vec4(finalColor, color.a);
                    }
                `,
                defaultUniforms: { intensity: 0.5, glowColor: [1.0, 1.0, 0.5], time: 0.0 },
                autoUpdate: true
            },
            'advancedColor': {
                vertexShader,
                fragmentShader: `
                    varying vec2 vTextureCoord;
                    uniform sampler2D uSampler;
                    uniform float contrast;
                    uniform float brightness;
                    uniform float shadows;
                    uniform float highlights;
                    uniform float saturation;
                    uniform float vibrance;

                    float getLuminance(vec3 color) {
                        return dot(color, vec3(0.299, 0.587, 0.114));
                    }

                    void main(void) {
                        vec4 color = texture2D(uSampler, vTextureCoord);
                        vec3 rgb = color.rgb;
                        rgb = rgb * brightness;
                        rgb = (rgb - 0.5) * contrast + 0.5;
                        float luminance = getLuminance(rgb);
                        if (luminance < 0.5) {
                            float shadowsEffect = (0.5 - luminance) * 2.0;
                            rgb = mix(rgb, vec3(0.0), -shadows * shadowsEffect);
                        }
                        if (luminance > 0.5) {
                            float highlightsEffect = (luminance - 0.5) * 2.0;
                            rgb = mix(rgb, vec3(1.0), highlights * highlightsEffect);
                        }
                        float luminance2 = getLuminance(rgb);
                        vec3 satColor = mix(vec3(luminance2), rgb, saturation);
                        float satLevel = length(rgb - vec3(luminance2)) / sqrt(3.0);
                        vec3 vibColor = mix(vec3(luminance2), rgb, 1.0 + vibrance * (1.0 - satLevel));
                        rgb = mix(satColor, vibColor, 0.5);
                        rgb = clamp(rgb, 0.0, 1.0);
                        gl_FragColor = vec4(rgb, color.a);
                    }
                `,
                defaultUniforms: {
                    contrast: 1.0,
                    brightness: 1.0,
                    shadows: 0.0,
                    highlights: 0.0,
                    saturation: 1.0,
                    vibrance: 0.0
                },
                autoUpdate: false
            }
        };

        return configs[filterType] || null;
    }

    // 工具函数：创建滤镜数组
    function createFiltersArray(filtersData) {
        if (!Array.isArray(filtersData)) {
            return [];
        }

        const filters = [];
        for (const filterData of filtersData) {
            if (filterData && filterData.type && filterData.params) {
                const filter = createFilterInstance(filterData.type, filterData.params);
                if (filter) {
                    filters.push(filter);
                }
            }
        }

        if (DEBUG) log(`[Filter] 创建了 ${filters.length} 个滤镜`);
        return filters;
    }

    // 工具函数：创建游戏对象
    function createGameObject(type, params = {}) {
        if (DEBUG) log(`[GameObject] Creating new object: ${type}`, params);

        switch (type) {
            case "Sprite":
                const sprite = new Sprite();
                sprite.name = params.name || "NewSprite";
                sprite.x = params.x || 0;
                sprite.y = params.y || 0;
                sprite.visible = params.visible !== undefined ? params.visible : true;
                return sprite;

            case "Label":
                const label = new Sprite();
                label.name = params.name || "NewLabel";
                label.x = params.x || 0;
                label.y = params.y || 0;
                label.visible = params.visible !== undefined ? params.visible : true;

                const bitmap = new Bitmap(200, 40);
                bitmap.fontSize = 20;
                bitmap.textColor = "#ffffff";
                bitmap.outlineColor = "rgba(0, 0, 0, 0.5)";
                bitmap.outlineWidth = 4;
                bitmap.drawText(params.text || "New Text", 0, 0, 200, 40, "left");
                bitmap.text = params.text || "New Text";

                label.bitmap = bitmap;
                return label;

            case "Container":
                const container = new PIXI.Container();
                container.name = params.name || "NewContainer";
                container.x = params.x || 0;
                container.y = params.y || 0;
                container.visible = params.visible !== undefined ? params.visible : true;
                return container;

            case "Window":
                const rect = new Rectangle(0, 0, 200, 100);
                const windowObj = new Window_Base(rect);
                windowObj.name = params.name || "NewWindow";
                windowObj.x = params.x || 0;
                windowObj.y = params.y || 0;
                windowObj.visible = params.visible !== undefined ? params.visible : true;
                return windowObj;

            case "Button":
                const button = new Sprite_Clickable();
                button.name = params.name || "NewButton";
                button.x = params.x || 0;
                button.y = params.y || 0;
                button.visible = params.visible !== undefined ? params.visible : true;

                const buttonBitmap = new Bitmap(120, 40);
                buttonBitmap.fillRect(0, 0, 120, 40, "#3498db");
                buttonBitmap.fontSize = 18;
                buttonBitmap.textColor = "#ffffff";
                buttonBitmap.drawText(params.text || "Button", 0, 0, 120, 40, "center");

                button.bitmap = buttonBitmap;
                button._isButton = true;
                return button;

            case "LayoutContainer":
                if (typeof LayoutContainer !== "undefined") {
                    const layoutContainer = new LayoutContainer();
                    layoutContainer.name = params.name || "NewLayoutContainer";
                    layoutContainer.x = params.x || 0;
                    layoutContainer.y = params.y || 0;
                    layoutContainer.visible = params.visible !== undefined ? params.visible : true;
                    return layoutContainer;
                } else {
                    // 如果没有LayoutContainer类，创建一个普通的Container
                    const fallbackContainer = new PIXI.Container();
                    fallbackContainer.name = params.name || "NewLayoutContainer";
                    fallbackContainer.x = params.x || 0;
                    fallbackContainer.y = params.y || 0;
                    fallbackContainer.visible = params.visible !== undefined ? params.visible : true;
                    return fallbackContainer;
                }

            default:
                if (DEBUG) log(`[GameObject] Unknown object type: ${type}`);
                return null;
        }
    }
// ==================== Scene_Title 场景修改 ====================

// 修改 Scene_Title 场景
const originalScene_TitleStart = Scene_Title.prototype.start;
Scene_Title.prototype.start = function() {
    // 调用原始方法
    originalScene_TitleStart.apply(this, arguments);

    if (DEBUG) console.log('Scene_Title.start 被调用');

    // 设置 Sprite 的 4 个属性
    const target_sprite_Scene_Title_2Path = ["Scene_Title","2"];
    const target_sprite_Scene_Title_2 = findObjectByScenePath(target_sprite_Scene_Title_2Path);
    if (target_sprite_Scene_Title_2) {
        // 设置滤镜[0].type
        if (target_sprite_Scene_Title_2.filters && target_sprite_Scene_Title_2.filters[0]) {
            target_sprite_Scene_Title_2.filters[0].type = "blur";
            if (DEBUG) console.log('设置滤镜属性:', 'filters[0].type', "blur");
        } else {
            if (DEBUG) console.warn('滤镜[0]不存在，无法设置type属性');
        }
        // 设置滤镜[0].enabled
        if (target_sprite_Scene_Title_2.filters && target_sprite_Scene_Title_2.filters[0]) {
            target_sprite_Scene_Title_2.filters[0].enabled = true;
            if (DEBUG) console.log('设置滤镜属性:', 'filters[0].enabled', true);
        } else {
            if (DEBUG) console.warn('滤镜[0]不存在，无法设置enabled属性');
        }
        // 设置滤镜[0].blur
        if (target_sprite_Scene_Title_2.filters && target_sprite_Scene_Title_2.filters[0]) {
            target_sprite_Scene_Title_2.filters[0].blur = 8;
            if (DEBUG) console.log('设置滤镜属性:', 'filters[0].blur', 8);
        } else {
            if (DEBUG) console.warn('滤镜[0]不存在，无法设置blur属性');
        }
        // 设置滤镜[0].quality
        if (target_sprite_Scene_Title_2.filters && target_sprite_Scene_Title_2.filters[0]) {
            target_sprite_Scene_Title_2.filters[0].quality = 4;
            if (DEBUG) console.log('设置滤镜属性:', 'filters[0].quality', 4);
        } else {
            if (DEBUG) console.warn('滤镜[0]不存在，无法设置quality属性');
        }
    }

};

// ==================== 代码生成完成 ====================
})();