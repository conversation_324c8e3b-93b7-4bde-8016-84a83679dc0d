import React, { useState, useRef } from "react";
import { TextField, Box, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";

// 静态变量，用于跟踪拖拽状态
const DragState = {
  isDragging: false,
  startValue: 0,
  initialValue: 0, // 鼠标按下时的初始值，用于记录历史
  currentValue: 0, // 当前滑动值，用于在鼠标抬起时使用
  startX: 0,
  currentOnChange: null as ((newValue: number, oldValue?: number) => void) | null,
  currentOnMove: null as ((newValue: number) => void) | null, // 滑动过程中的回调
  step: 1,
  min: undefined as number | undefined,
  max: undefined as number | undefined,
  precision: 2
};

interface NumberInputProps {
  label: string;
  value: number | undefined;
  onChange: (newValue: number, oldValue?: number) => void; // 只在操作完成时触发一次
  onMove?: (newValue: number) => void; // 在滑动过程中触发
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
  width?: string | number;
}

// 使用 styled API 创建自定义样式的 Box 组件
const StyledBox = styled(Box)(({ theme }) => ({
  cursor: "ew-resize",
  userSelect: "none",
  padding: 0,
  margin: 0,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  height: "16px",
  color: "rgba(64, 105, 240, 0.9)",
  position: "relative",
  "&::after": {
    content: "''",
    position: "absolute",
    bottom: 0,
    left: 0,
    width: "100%",
    height: "1px",
    backgroundColor: "rgba(64, 105, 240, 0.9)",
    zIndex: 2,
  },
  "&:hover": {
    backgroundColor: theme.palette.action.hover,
  },
}));

const NumberInput: React.FC<NumberInputProps> = ({
  label,
  value = 0,
  onChange,
  onMove,
  min,
  max,
  step = 1,
  precision = 2,
  width = "100%",
}) => {
  // 状态
  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState<string>(value?.toFixed(precision) || "0");

  // 输入框引用
  const inputRef = useRef<HTMLInputElement>(null);

  // 双击进入编辑模式
  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  // 引用，用于指针捕获
  const elementRef = useRef<HTMLDivElement | null>(null);

  // 鼠标按下开始拖动
  const handlePointerDown = (e: React.PointerEvent) => {
    // 如果正在编辑或者已经有其他组件在拖拽中，则不处理
    if (isEditing || DragState.isDragging) return;

    console.log("NumberInput - 开始拖拽:", label, value);

    // 存储拖拽状态
    DragState.isDragging = true;
    DragState.startValue = value || 0;
    DragState.initialValue = value || 0; // 记录初始值，用于历史记录
    DragState.currentValue = value || 0; // 初始化当前值
    DragState.startX = e.clientX;
    DragState.currentOnChange = onChange;
    DragState.currentOnMove = onMove || null; // 存储 onMove 回调
    DragState.step = step;
    DragState.min = min;
    DragState.max = max;
    DragState.precision = precision;

    console.log("NumberInput - 记录初始值:", DragState.initialValue);

    // 设置指针捕获，确保即使鼠标移出元素也能接收事件
    if (e.currentTarget && 'setPointerCapture' in e.currentTarget) {
      try {
        e.currentTarget.setPointerCapture(e.pointerId);
        elementRef.current = e.currentTarget as HTMLDivElement;
      } catch (err) {
        console.error("Error setting pointer capture:", err);
      }
    }

    // 定义鼠标移动处理函数
    const handleMouseMove = (moveEvent: MouseEvent | PointerEvent) => {
      moveEvent.preventDefault();
      if (!DragState.isDragging) return;

      // 计算拖动距离
      const diff = moveEvent.clientX - DragState.startX;
      const sensitivity = 1.0;

      // 计算新值
      let newValue = DragState.startValue + diff * sensitivity * DragState.step;

      // 应用最小/最大限制
      if (DragState.min !== undefined) newValue = Math.max(DragState.min, newValue);
      if (DragState.max !== undefined) newValue = Math.min(DragState.max, newValue);

      // 应用精度
      newValue = Number(newValue.toFixed(DragState.precision));

      // 更新当前值
      DragState.currentValue = newValue;

      // 始终触发更新，即使值没有变化
      console.log(`NumberInput - 拖拽更新 ${label}: ${newValue}, 初始值: ${DragState.initialValue}, 组件值: ${value}`);

      // 使用 onMove 回调进行实时更新
      if (DragState.currentOnMove) {
        DragState.currentOnMove(newValue);
      }

      // 不再在这里调用 onChange，而是在鼠标抬起时调用
    };



    // 定义鼠标释放处理函数
    const handleMouseUp = (upEvent: MouseEvent | PointerEvent) => {
      console.log("NumberInput - 结束拖拽:", label);

      // 释放指针捕获
      if (elementRef.current && 'releasePointerCapture' in elementRef.current) {
        try {
          const pointerId = (upEvent as PointerEvent).pointerId;
          if (pointerId) {
            elementRef.current.releasePointerCapture(pointerId);
          }
        } catch (err) {
          console.error("Error releasing pointer capture:", err);
        }
      }

      // 在鼠标抬起时调用 onChange，传递最终值和初始值
      // 无论值是否变化，都触发 onChange
      console.log("NumberInput - 检查 onChange 条件:", {
        isDragging: DragState.isDragging,
        hasOnChange: !!DragState.currentOnChange,
        componentValue: value,
        currentValue: DragState.currentValue,
        initialValue: DragState.initialValue
      });

      if (DragState.isDragging && DragState.currentOnChange) {
        // 使用 currentValue 而不是 value
        console.log(`NumberInput - 拖拽完成，调用 onChange: ${DragState.currentValue} (初始值: ${DragState.initialValue})`);



        // 调用 onChange 回调
        DragState.currentOnChange(DragState.currentValue, DragState.initialValue);
      } else {
        console.warn("NumberInput - 未能调用 onChange，条件不满足");
      }

      // 重置拖拽状态
      DragState.isDragging = false;
      DragState.currentOnChange = null;
      DragState.currentOnMove = null; // 重置 onMove 回调

      // 移除事件监听器
      window.removeEventListener("pointermove", handleMouseMove);
      window.removeEventListener("pointerup", handleMouseUp);
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);

      // 恢复鼠标样式
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    // 添加事件监听
    window.addEventListener("pointermove", handleMouseMove);
    window.addEventListener("pointerup", handleMouseUp);
    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mouseup", handleMouseUp);

    // 设置鼠标样式
    document.body.style.cursor = 'ew-resize';
    document.body.style.userSelect = 'none';

    // 防止文本选择
    e.preventDefault();
  };

  // 输入框变化处理
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
  };

  // 输入框失焦处理
  const handleBlur = () => {
    setIsEditing(false);

    // 解析输入值
    const newValue = parseFloat(inputValue);
    if (!isNaN(newValue)) {
      // 应用最小/最大限制
      let validValue = newValue;
      if (min !== undefined) validValue = Math.max(min, validValue);
      if (max !== undefined) validValue = Math.min(max, validValue);

      // 记录当前值作为旧值
      const oldValue = value || 0;
      console.log(`NumberInput - 输入完成: ${validValue}, 原始值: ${oldValue}`);

      // 传递新值和旧值
      onChange(validValue, oldValue);
    } else {
      // 如果输入无效，恢复原值
      setInputValue(value?.toFixed(precision) || "0");
    }
  };

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleBlur();
    } else if (e.key === "Escape") {
      setInputValue(value?.toFixed(precision) || "0");
      setIsEditing(false);
    }
  };

  // 当值变化时更新输入值
  React.useEffect(() => {
    if (!isEditing) {
      setInputValue(value?.toFixed(precision) || "0");
    }
  }, [value, precision, isEditing]);

  // 当进入编辑模式时聚焦输入框
  React.useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // 添加额外的安全措施，确保在iframe或其他元素获得焦点时也能正确处理鼠标事件
  React.useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden' && DragState.isDragging) {
        // 如果页面不可见且正在拖动，强制结束拖动
        console.log("页面不可见，强制结束拖动");
        DragState.isDragging = false;
        DragState.currentOnChange = null;
        DragState.currentOnMove = null; // 重置 onMove 回调
      }
    };

    const handleBlur = () => {
      // 当窗口失去焦点时，如果正在拖动，强制结束拖动
      if (DragState.isDragging) {
        console.log("窗口失焦，强制结束拖动");
        DragState.isDragging = false;
        DragState.currentOnChange = null;
        DragState.currentOnMove = null; // 重置 onMove 回调
      }
    };

    // 监听页面可见性变化和窗口失焦事件
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('blur', handleBlur);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('blur', handleBlur);
    };
  }, []);

  return (
    <Box sx={{ width, mb: 0.5 }}>
      {isEditing ? (
        <TextField
          inputRef={inputRef}
          value={inputValue}
          onChange={handleChange}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          size="small"
          type="number"
          autoFocus
          sx={{
            width: '100%',
            '& .MuiOutlinedInput-root': {
              height: 24,
              backgroundColor: 'rgba(0, 0, 0, 0.2)',
            },
            '& input': {
              padding: 0,
              textAlign: 'center',
              fontSize: '12px',
              color: 'rgba(64, 105, 240, 0.9)',
            },
            '& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button': {
              display: 'none',
            },
          }}
        />
      ) : (
        <Box
          className="inspector-row"
          display="flex"
          alignItems="center"
          gap={0}
          sx={{ height: "24px" }}
        >
          {/* 标签 */}
          <Typography
            variant="body2"
            sx={{
              fontSize: '12px',
              color: 'text.secondary',
              width: '50%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {label}
          </Typography>

          {/* 值显示区域 */}
          <Box sx={{ flex: 1 }}>
            <StyledBox
              onDoubleClick={handleDoubleClick}
              onPointerDown={handlePointerDown}
            >
              <Typography
                variant="body1"
                sx={{
                  margin: 0,
                  padding: 0,
                  lineHeight: '16px',
                  fontSize: '12px',
                  height: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  justifyContent: 'center',
                  backgroundColor: 'transparent',
                }}
              >
                {value?.toFixed(precision) || "0"}
              </Typography>
            </StyledBox>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default NumberInput;
