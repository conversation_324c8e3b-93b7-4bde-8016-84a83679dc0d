import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON>,
  Slider,
  <PERSON><PERSON><PERSON>,
  TextField,
  Tooltip,
  IconButton,
  Fade,
  Paper
} from "@mui/material";
import { styled } from "@mui/material/styles";
import CheckIcon from "@mui/icons-material/Check";

// 组件属性类型定义
interface SliderInputProps {
  label?: string;
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  width?: string | number;
  disabled?: boolean;
  showInput?: boolean;
  marks?: boolean | { value: number; label: string }[];
  valueLabelDisplay?: "auto" | "on" | "off";
  compact?: boolean;
  unit?: string;
}

// 自定义样式的滑块
const StyledSlider = styled(Slider)(({ theme }) => ({
  color: theme.palette.primary.main,
  height: 8,
  padding: "15px 0",
  "& .MuiSlider-thumb": {
    height: 16,
    width: 16,
    backgroundColor: "#fff",
    border: `2px solid ${theme.palette.primary.main}`,
    "&:focus, &:hover, &.Mui-active": {
      boxShadow: `0 0 0 8px ${theme.palette.primary.main}30`,
    },
    "&:before": {
      display: "none",
    },
  },
  "& .MuiSlider-track": {
    height: 8,
    borderRadius: 4,
  },
  "& .MuiSlider-rail": {
    height: 8,
    borderRadius: 4,
    opacity: 0.5,
    backgroundColor: theme.palette.mode === "dark" ? "#bfbfbf" : "#d8d8d8",
  },
  "& .MuiSlider-mark": {
    backgroundColor: theme.palette.background.paper,
    height: 8,
    width: 2,
    marginTop: 0,
  },
  "& .MuiSlider-markActive": {
    opacity: 1,
    backgroundColor: "currentColor",
  },
  "& .MuiSlider-valueLabel": {
    fontSize: 12,
    fontWeight: "normal",
    top: -10,
    backgroundColor: theme.palette.primary.main,
    "&:before": {
      display: "none",
    },
    "& *": {
      background: "transparent",
      color: theme.palette.common.white,
    },
  },
}));

// 紧凑模式的滑块容器
const CompactSliderContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.divider}`,
  transition: "all 0.2s",
  "&:hover": {
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.action.hover,
  },
}));

// 滑块组件
const SliderInput: React.FC<SliderInputProps> = ({
  label,
  value,
  onChange,
  min = 0,
  max = 100,
  step = 1,
  width = "100%",
  disabled = false,
  showInput = true,
  marks = false,
  valueLabelDisplay = "auto",
  compact = false,
  unit = "",
}) => {
  // 状态
  const [inputValue, setInputValue] = useState<string>(value.toString());
  const [showTooltip, setShowTooltip] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  // 引用
  const tooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const prevValueRef = useRef<number>(value);

  // 当value变化时，更新inputValue
  useEffect(() => {
    setInputValue(value.toString());
  }, [value]);

  // 处理滑块变化
  const handleSliderChange = (_event: Event, newValue: number | number[]) => {
    const numValue = Array.isArray(newValue) ? newValue[0] : newValue;
    setInputValue(numValue.toString());
    onChange(numValue);
  };

  // 处理滑块拖动开始
  const handleSliderDragStart = () => {
    setIsDragging(true);
    prevValueRef.current = value;
  };

  // 处理滑块拖动结束
  const handleSliderDragEnd = () => {
    setIsDragging(false);

    // 显示成功提示
    setShowTooltip(true);
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current);
    }
    tooltipTimeoutRef.current = setTimeout(() => {
      setShowTooltip(false);
    }, 1500);
  };

  // 处理输入框变化
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
  };

  // 处理输入框失焦
  const handleInputBlur = () => {
    let numValue = parseFloat(inputValue);

    // 验证输入值是否有效
    if (isNaN(numValue)) {
      numValue = value;
      setInputValue(value.toString());
    } else {
      // 限制在min和max范围内
      numValue = Math.max(min, Math.min(max, numValue));
      setInputValue(numValue.toString());
    }

    // 如果值有变化，更新
    if (numValue !== value) {
      onChange(numValue);

      // 显示成功提示
      setShowTooltip(true);
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
      tooltipTimeoutRef.current = setTimeout(() => {
        setShowTooltip(false);
      }, 1500);
    }
  };

  // 处理输入框按键
  const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      (event.target as HTMLInputElement).blur();
    }
  };

  // 紧凑模式渲染
  if (compact) {
    return (
      <Box sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        width,
        mb: 1
      }}>
        {label && (
          <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
            {label}
          </Typography>
        )}

        <Box sx={{ position: "relative", width: label ? "70%" : "100%" }}>
          <CompactSliderContainer
            sx={{
              opacity: disabled ? 0.6 : 1,
              pointerEvents: disabled ? "none" : "auto",
            }}
          >
            <StyledSlider
              value={value}
              onChange={handleSliderChange}
              onMouseDown={handleSliderDragStart}
              onMouseUp={handleSliderDragEnd}
              onTouchStart={handleSliderDragStart}
              onTouchEnd={handleSliderDragEnd}
              aria-labelledby={label ? `slider-${label}` : undefined}
              min={min}
              max={max}
              step={step}
              marks={marks}
              valueLabelDisplay={valueLabelDisplay}
              disabled={disabled}
              sx={{
                mx: 1,
                width: showInput ? "calc(100% - 60px)" : "100%",
              }}
            />

            {showInput && (
              <TextField
                value={inputValue}
                onChange={handleInputChange}
                onBlur={handleInputBlur}
                onKeyDown={handleInputKeyDown}
                disabled={disabled}
                size="small"
                variant="standard"
                InputProps={{
                  endAdornment: unit ? (
                    <Typography variant="caption" color="text.secondary">
                      {unit}
                    </Typography>
                  ) : null,
                  sx: {
                    fontSize: "0.75rem",
                    width: 50,
                    p: 0,
                    "& input": { textAlign: "center" }
                  }
                }}
              />
            )}
          </CompactSliderContainer>

          <Tooltip
            open={showTooltip}
            title="已更新"
            placement="top"
            arrow
            TransitionComponent={Fade}
            TransitionProps={{ timeout: 300 }}
          >
            <IconButton
              size="small"
              sx={{
                position: "absolute",
                top: -10,
                right: -10,
                backgroundColor: "success.main",
                color: "white",
                width: 20,
                height: 20,
                opacity: showTooltip ? 1 : 0,
                transition: "opacity 0.3s",
                "&:hover": {
                  backgroundColor: "success.dark",
                },
                pointerEvents: "none"
              }}
            >
              <CheckIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    );
  }

  // 标准模式渲染
  return (
    <Box sx={{ width, mb: 1 }}>
      {label && (
        <Typography
          id={`slider-${label}`}
          variant="body2"
          color="text.secondary"
          gutterBottom
        >
          {label}: {value}{unit}
        </Typography>
      )}

      <Box sx={{
        display: "flex",
        alignItems: "center",
        opacity: disabled ? 0.6 : 1,
        pointerEvents: disabled ? "none" : "auto",
      }}>
        <StyledSlider
          value={value}
          onChange={handleSliderChange}
          onMouseDown={handleSliderDragStart}
          onMouseUp={handleSliderDragEnd}
          onTouchStart={handleSliderDragStart}
          onTouchEnd={handleSliderDragEnd}
          aria-labelledby={label ? `slider-${label}` : undefined}
          min={min}
          max={max}
          step={step}
          marks={marks}
          valueLabelDisplay={valueLabelDisplay}
          disabled={disabled}
          sx={{
            mx: 1,
            width: showInput ? "calc(100% - 80px)" : "100%",
          }}
        />

        {showInput && (
          <TextField
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            onKeyDown={handleInputKeyDown}
            disabled={disabled}
            size="small"
            variant="outlined"
            InputProps={{
              endAdornment: unit ? (
                <Typography variant="caption" color="text.secondary">
                  {unit}
                </Typography>
              ) : null,
              sx: {
                width: 70,
                "& input": { textAlign: "center" }
              }
            }}
          />
        )}
      </Box>
    </Box>
  );
};

export default SliderInput;
