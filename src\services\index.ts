/**
 * 服务层统一导出
 * 统一使用新的generators API架构
 */

// 导入generators的新API
import {
  BaseTempObj,
  OperationManager,
  globalOperationManager,
  setObjectProperty,
  getObjectProperty,
  generateAllCode,
  generateCodeByScene,
  clearAllOperations,
  getOperationStatistics,
  serializeOperations,
  deserializeOperations,
  createOperation,
  getOperation,
  removeOperation,
  hasOperation,
  getAllOperations,
  getModifiedOperations
} from '../generators';

// 重新导出generators的新API
export {
  BaseTempObj,
  OperationManager,
  globalOperationManager,
  setObjectProperty,
  getObjectProperty,
  generateAllCode,
  generateCodeByScene,
  clearAllOperations,
  getOperationStatistics,
  serializeOperations,
  deserializeOperations,
  createOperation,
  getOperation,
  removeOperation,
  hasOperation,
  getAllOperations,
  getModifiedOperations
};

// 导入并导出BackendService
import { BackendService } from './BackendService';
export { BackendService };
export type { SaveCodeRequest, SaveCodeResponse, RefreshPreviewRequest, RefreshPreviewResponse } from './BackendService';

/**
 * 统一的属性设置服务
 * 替代所有旧的属性服务
 */
export class PropertyService {
  /**
   * 设置对象属性
   */
  static setProperty(
    object: any,
    propertyName: string,
    value: any,
    objectPath?: string[],
    className?: string
  ): void {
    // 如果没有提供路径，尝试从对象获取
    const path = objectPath || object._rpgEditorPath || ['Unknown'];
    const cls = className || object.constructor?.name || 'Unknown';

    // 使用新的generators API
    setObjectProperty(path, cls, propertyName, value);

    // 同时更新对象本身（用于实时预览）
    if (object && propertyName in object) {
      object[propertyName] = value;
    }
  }

  /**
   * 获取对象属性
   */
  static getProperty(
    objectPath: string[],
    className: string,
    propertyName: string
  ): any {
    return getObjectProperty(objectPath, className, propertyName);
  }

  /**
   * 批量设置属性
   */
  static setProperties(
    object: any,
    properties: Record<string, any>,
    objectPath?: string[],
    className?: string
  ): void {
    for (const [name, value] of Object.entries(properties)) {
      this.setProperty(object, name, value, objectPath, className);
    }
  }

  /**
   * 生成代码
   */
  static generateCode(): string {
    return generateAllCode();
  }

  /**
   * 按场景生成代码
   */
  static generateCodeByScene(): Map<string, string> {
    return generateCodeByScene();
  }

  /**
   * 保存操作记录
   */
  static saveOperations(): string {
    return serializeOperations();
  }

  /**
   * 加载操作记录
   */
  static loadOperations(jsonString: string): void {
    deserializeOperations(jsonString);
  }

  /**
   * 清空所有操作
   */
  static clearAll(): void {
    clearAllOperations();
  }

  /**
   * 获取统计信息
   */
  static getStatistics() {
    return getOperationStatistics();
  }
}
