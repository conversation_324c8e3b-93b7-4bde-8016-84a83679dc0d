/**
 * 测试新的generators架构
 * 基于BaseTempObj和OperationManager的重新实现
 */

import { 
  BaseTempObj, 
  OperationManager,
  setObjectProperty,
  generateAllCode,
  createOperation,
  getOperationStatistics,
  serializeOperations,
  deserializeOperations
} from '../index';

export function testNewArchitecture(): void {
  console.log('🧪 测试新的generators架构...');
  
  // 创建操作管理器
  const manager = new OperationManager();
  
  // 测试1: 创建BaseTempObj
  console.log('\n1. 测试创建BaseTempObj...');
  const obj1 = new BaseTempObj(['Scene_Title', '2'], 'Sprite', 'modify');
  
  // 设置一些属性
  obj1.setProperty('x', 100);
  obj1.setProperty('y', 200);
  obj1.setProperty('alpha', 0.8);
  
  console.log('- 创建对象:', obj1.getUniqueKey());
  console.log('- 修改的属性:', obj1.getModifiedProperties());
  
  // 测试2: 生成代码
  console.log('\n2. 测试代码生成...');
  const code1 = obj1.generateCode();
  console.log('生成的代码:');
  console.log(code1);
  
  // 测试3: 测试bitmap.elements
  console.log('\n3. 测试bitmap.elements处理...');
  const obj2 = new BaseTempObj(['Scene_Menu', '1', '3', '1', '2'], 'Sprite', 'modify');
  
  const elementsValue = [
    {
      "type": "text",
      "text": "里德",
      "x": 184,
      "y": 13,
      "maxWidth": 168,
      "lineHeight": 36,
      "align": "left"
    },
    {
      "type": "image",
      "source": {
        "_url": "../projects/Project4/img/faces/Actor1.png",
        "width": 144,
        "height": 129
      },
      "sx": 432,
      "sy": 7,
      "sw": 144,
      "sh": 129,
      "dx": 5,
      "dy": 138,
      "dw": 144,
      "dh": 129
    }
  ];
  
  obj2.setProperty('_bitmap.elements', elementsValue);
  
  const code2 = obj2.generateCode();
  console.log('bitmap.elements代码:');
  console.log(code2);
  
  // 测试4: 操作管理器
  console.log('\n4. 测试操作管理器...');
  manager.addOperation(['Scene_Title', '1'], 'Sprite');
  manager.setProperty(['Scene_Title', '1'], 'Sprite', 'x', 50);
  manager.setProperty(['Scene_Title', '1'], 'Sprite', 'y', 75);
  
  manager.addOperation(['Scene_Menu', '2'], 'Container');
  manager.setProperty(['Scene_Menu', '2'], 'Container', 'alpha', 0.5);
  
  console.log('- 操作数量:', manager.size());
  console.log('- 统计信息:', manager.getStatistics());
  
  const allCode = manager.generateAllCode();
  console.log('所有操作的代码:');
  console.log(allCode);
  
  // 测试5: 按场景分组
  console.log('\n5. 测试按场景分组...');
  const codeByScene = manager.generateCodeByScene();
  for (const [sceneName, sceneCode] of codeByScene) {
    console.log(`场景 ${sceneName}:`);
    console.log(sceneCode);
    console.log('---');
  }
  
  // 测试6: 序列化和反序列化
  console.log('\n6. 测试序列化...');
  const serialized = manager.toJSON();
  console.log('序列化数据:', JSON.stringify(serialized, null, 2));
  
  const newManager = new OperationManager();
  newManager.fromJSON(serialized);
  console.log('反序列化后的操作数量:', newManager.size());
  
  // 测试7: 全局便捷方法
  console.log('\n7. 测试全局便捷方法...');
  setObjectProperty(['Scene_Battle', '1'], 'Sprite', 'rotation', 0.5);
  setObjectProperty(['Scene_Battle', '1'], 'Sprite', 'tint', 0xff0000);
  
  const globalCode = generateAllCode();
  console.log('全局代码:');
  console.log(globalCode);
  
  const stats = getOperationStatistics();
  console.log('全局统计:', stats);
  
  // 测试8: JSON序列化
  console.log('\n8. 测试JSON序列化...');
  const jsonString = serializeOperations();
  console.log('JSON序列化长度:', jsonString.length);
  
  // 清空并反序列化
  console.log('清空操作...');
  const emptyCode = generateAllCode();
  console.log('清空后的代码:', emptyCode);
  
  console.log('反序列化...');
  deserializeOperations(jsonString);
  const restoredCode = generateAllCode();
  console.log('恢复后的代码:');
  console.log(restoredCode);
  
  // 测试9: 颜色属性
  console.log('\n9. 测试颜色属性...');
  const colorObj = createOperation(['Scene_Title', '3'], 'Sprite');
  colorObj.setProperty('colorTone', [255, 0, 0, 128]);
  colorObj.setProperty('blendColor', [0, 255, 0, 64]);
  
  const colorCode = colorObj.generateCode();
  console.log('颜色属性代码:');
  console.log(colorCode);
  
  // 测试10: 复杂bitmap属性
  console.log('\n10. 测试复杂bitmap属性...');
  const bitmapObj = createOperation(['Scene_Status', '1'], 'Sprite');
  bitmapObj.setProperty('_bitmap.fontSize', 24);
  bitmapObj.setProperty('_bitmap.textColor', '#ffffff');
  bitmapObj.setProperty('_bitmap.outlineWidth', 2);
  
  const bitmapCode = bitmapObj.generateCode();
  console.log('bitmap属性代码:');
  console.log(bitmapCode);
  
  console.log('\n✅ 新架构测试完成！');
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testNewArchitecture();
}
