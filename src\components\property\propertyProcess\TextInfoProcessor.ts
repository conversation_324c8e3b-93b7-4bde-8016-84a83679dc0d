/**
 * 文字信息组件处理器
 * 处理文字相关属性变化并应用到对象上
 */



export interface TextInfoProperties {
  text?: string;
  fontSize?: number;
  fontFace?: string;
  fontBold?: boolean;
  fontItalic?: boolean;
  textColor?: string;
  outlineColor?: string;
  outlineWidth?: number;
  textAlign?: 'left' | 'center' | 'right';
  textBaseline?: 'top' | 'middle' | 'bottom';
  wordWrap?: boolean;
  wordWrapWidth?: number;
  lineHeight?: number;
}

export class TextInfoProcessor {
  /**
   * 应用文字属性到对象
   */
  static applyProperty(
    object: any,
    propertyName: keyof TextInfoProperties,
    value: any
  ): void {
    if (!object) {
      console.warn('TextInfoProcessor: 对象为空');
      return;
    }

    // 检查是否是文字对象
    if (!this.isTextObject(object)) {
      console.warn('TextInfoProcessor: 对象不是文字类型');
      return;
    }

    try {
      // 处理特殊文字属性
      if (this.handleSpecialTextProperty(object, propertyName, value)) {
        console.log(`TextInfoProcessor: 已应用文字属性 ${propertyName} 到对象 ${object.constructor?.name}`);
        return;
      }

      // 直接设置对象属性（用于实时预览）
      if (propertyName in object) {
        object[propertyName] = value;
      }

      console.log(`TextInfoProcessor: 已应用属性 ${propertyName} = ${value} 到对象 ${object.constructor?.name}`);
    } catch (error) {
      console.error(`TextInfoProcessor: 应用属性 ${propertyName} 失败:`, error);
    }
  }

  /**
   * 批量应用文字属性
   */
  static applyProperties(
    object: any,
    properties: Partial<TextInfoProperties>
  ): void {
    if (!object) {
      console.warn('TextInfoProcessor: 对象为空');
      return;
    }

    if (!this.isTextObject(object)) {
      console.warn('TextInfoProcessor: 对象不是文字类型');
      return;
    }

    for (const [propertyName, value] of Object.entries(properties)) {
      if (value !== undefined && value !== null) {
        this.applyProperty(object, propertyName as keyof TextInfoProperties, value);
      }
    }
  }

  /**
   * 检查是否是文字对象
   */
  private static isTextObject(object: any): boolean {
    return object && (
      object.constructor?.name === 'Sprite_Text' ||
      object.constructor?.name === 'Window_Base' ||
      object.constructor?.name === 'Bitmap' ||
      typeof object.drawText === 'function' ||
      object.text !== undefined ||
      object.fontSize !== undefined
    );
  }

  /**
   * 处理特殊文字属性设置
   */
  static handleSpecialTextProperty(
    object: any,
    propertyName: keyof TextInfoProperties,
    value: any
  ): boolean {
    switch (propertyName) {
      case 'text':
        return this.handleTextChange(object, value);

      case 'fontSize':
        return this.handleFontSizeChange(object, value);

      case 'fontFace':
        return this.handleFontFaceChange(object, value);

      case 'fontBold':
      case 'fontItalic':
        return this.handleFontStyleChange(object, propertyName, value);

      case 'textColor':
      case 'outlineColor':
        return this.handleColorChange(object, propertyName, value);

      case 'outlineWidth':
        return this.handleOutlineWidthChange(object, value);

      case 'textAlign':
      case 'textBaseline':
        return this.handleAlignmentChange(object, propertyName, value);

      case 'wordWrap':
      case 'wordWrapWidth':
        return this.handleWordWrapChange(object, propertyName, value);

      default:
        return false;
    }
  }

  /**
   * 处理文字内容变化
   */
  private static handleTextChange(object: any, text: string): boolean {
    try {
      if (typeof object.drawText === 'function') {
        // 对于Bitmap对象，清除并重新绘制文字
        object.clear();
        object.drawText(text, 0, 0, object.width, object.height, 'left');
        return true;
      } else if (object.text !== undefined) {
        object.text = text;
        return true;
      }
      return false;
    } catch (error) {
      console.error('TextInfoProcessor: 处理文字变化失败:', error);
      return false;
    }
  }

  /**
   * 处理字体大小变化
   */
  private static handleFontSizeChange(object: any, fontSize: number): boolean {
    try {
      if (object.fontSize !== undefined) {
        object.fontSize = fontSize;
        // 如果有重绘方法，调用它
        if (typeof object.refresh === 'function') {
          object.refresh();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('TextInfoProcessor: 处理字体大小变化失败:', error);
      return false;
    }
  }

  /**
   * 处理字体名称变化
   */
  private static handleFontFaceChange(object: any, fontFace: string): boolean {
    try {
      if (object.fontFace !== undefined) {
        object.fontFace = fontFace;
        // 如果有重绘方法，调用它
        if (typeof object.refresh === 'function') {
          object.refresh();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('TextInfoProcessor: 处理字体名称变化失败:', error);
      return false;
    }
  }

  /**
   * 处理字体样式变化
   */
  private static handleFontStyleChange(object: any, propertyName: 'fontBold' | 'fontItalic', value: boolean): boolean {
    try {
      if (object[propertyName] !== undefined) {
        object[propertyName] = value;
        // 如果有重绘方法，调用它
        if (typeof object.refresh === 'function') {
          object.refresh();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error(`TextInfoProcessor: 处理字体样式变化失败 (${propertyName}):`, error);
      return false;
    }
  }

  /**
   * 处理颜色变化
   */
  private static handleColorChange(object: any, propertyName: 'textColor' | 'outlineColor', color: string): boolean {
    try {
      if (object[propertyName] !== undefined) {
        object[propertyName] = color;
        // 如果有重绘方法，调用它
        if (typeof object.refresh === 'function') {
          object.refresh();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error(`TextInfoProcessor: 处理颜色变化失败 (${propertyName}):`, error);
      return false;
    }
  }

  /**
   * 处理描边宽度变化
   */
  private static handleOutlineWidthChange(object: any, width: number): boolean {
    try {
      if (object.outlineWidth !== undefined) {
        object.outlineWidth = width;
        // 如果有重绘方法，调用它
        if (typeof object.refresh === 'function') {
          object.refresh();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('TextInfoProcessor: 处理描边宽度变化失败:', error);
      return false;
    }
  }

  /**
   * 处理对齐方式变化
   */
  private static handleAlignmentChange(
    object: any,
    propertyName: 'textAlign' | 'textBaseline',
    value: string
  ): boolean {
    try {
      if (object[propertyName] !== undefined) {
        object[propertyName] = value;
        // 如果有重绘方法，调用它
        if (typeof object.refresh === 'function') {
          object.refresh();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error(`TextInfoProcessor: 处理对齐方式变化失败 (${propertyName}):`, error);
      return false;
    }
  }

  /**
   * 处理自动换行变化
   */
  private static handleWordWrapChange(
    object: any,
    propertyName: 'wordWrap' | 'wordWrapWidth',
    value: boolean | number
  ): boolean {
    try {
      if (object[propertyName] !== undefined) {
        object[propertyName] = value;
        // 如果有重绘方法，调用它
        if (typeof object.refresh === 'function') {
          object.refresh();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error(`TextInfoProcessor: 处理自动换行变化失败 (${propertyName}):`, error);
      return false;
    }
  }

  /**
   * 验证文字属性值
   */
  static validateProperty(
    propertyName: keyof TextInfoProperties,
    value: any
  ): boolean {
    switch (propertyName) {
      case 'text':
      case 'fontFace':
      case 'textColor':
      case 'outlineColor':
        return typeof value === 'string';

      case 'fontSize':
      case 'outlineWidth':
      case 'wordWrapWidth':
      case 'lineHeight':
        return typeof value === 'number' && value > 0;

      case 'fontBold':
      case 'fontItalic':
      case 'wordWrap':
        return typeof value === 'boolean';

      case 'textAlign':
        return ['left', 'center', 'right'].includes(value);

      case 'textBaseline':
        return ['top', 'middle', 'bottom'].includes(value);

      default:
        return true;
    }
  }

  /**
   * 获取文字属性的默认值
   */
  static getDefaultValue(propertyName: keyof TextInfoProperties): any {
    switch (propertyName) {
      case 'text':
        return '';
      case 'fontSize':
        return 28;
      case 'fontFace':
        return 'GameFont';
      case 'fontBold':
      case 'fontItalic':
      case 'wordWrap':
        return false;
      case 'textColor':
        return '#ffffff';
      case 'outlineColor':
        return '#000000';
      case 'outlineWidth':
        return 4;
      case 'textAlign':
        return 'left';
      case 'textBaseline':
        return 'top';
      case 'wordWrapWidth':
        return 200;
      case 'lineHeight':
        return 36;
      default:
        return null;
    }
  }

  /**
   * 重新绘制文字对象
   */
  static refreshTextObject(object: any): void {
    try {
      if (typeof object.refresh === 'function') {
        object.refresh();
      } else if (typeof object.redraw === 'function') {
        object.redraw();
      } else if (typeof object.update === 'function') {
        object.update();
      }
    } catch (error) {
      console.error('TextInfoProcessor: 重新绘制文字对象失败:', error);
    }
  }
}
