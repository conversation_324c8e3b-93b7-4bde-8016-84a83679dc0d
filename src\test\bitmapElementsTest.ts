/**
 * 测试优化后的 bitmap.elements 代码生成
 */

import { BaseTempObj } from '../generators/core/baseTempObj';

// 模拟测试数据
const testElements = [
  {
    "type": "text",
    "text": "里德",
    "x": 77,
    "y": 9,
    "maxWidth": 168,
    "lineHeight": 36,
    "align": "left"
  },
  {
    "type": "image",
    "source": {
      "_url": "../projects/Project4/img/faces/Actor1.png",
      "width": 576,
      "height": 288
    },
    "sx": 432,
    "sy": 7,
    "sw": 144,
    "sh": 129,
    "dx": 257,
    "dy": 53,
    "dw": 144,
    "dh": 129
  },
  {
    "type": "text",
    "text": "米歇尔",
    "x": 184,
    "y": 148,
    "maxWidth": 168,
    "lineHeight": 36,
    "align": "left"
  },
  {
    "type": "image",
    "source": {
      "_url": "../projects/Project4/img/faces/Actor1.png", // 相同的图片
      "width": 576,
      "height": 288
    },
    "sx": 144,
    "sy": 151,
    "sw": 144,
    "sh": 129,
    "dx": 5,
    "dy": 273,
    "dw": 144,
    "dh": 129
  },
  {
    "type": "image",
    "source": {
      "_url": "../projects/Project4/img/faces/Actor1.png", // 又是相同的图片
      "width": 576,
      "height": 288
    },
    "sx": 288,
    "sy": 151,
    "sw": 144,
    "sh": 129,
    "dx": 5,
    "dy": 408,
    "dw": 144,
    "dh": 129
  },
  {
    "type": "image",
    "source": {
      "_url": "../projects/Project4/img/enemies/Actor1_3.png", // 不同的图片
      "width": 224,
      "height": 228
    },
    "sx": 0,
    "sy": 0,
    "sw": 224,
    "sh": 228,
    "dx": -55,
    "dy": 38,
    "dw": 224,
    "dh": 228
  }
];

/**
 * 测试优化后的代码生成
 */
export function testOptimizedBitmapElements(): void {
  console.log('🧪 测试优化后的 bitmap.elements 代码生成...');

  // 创建测试对象
  const testObj = new BaseTempObj(['Scene_Menu', '1', '3', '1', '2'], 'Sprite', 'modify');
  
  // 设置 _bitmap.elements 属性
  testObj.setProperty('_bitmap.elements', testElements);
  
  // 生成代码
  const generatedCode = testObj.generateCode();
  
  console.log('生成的代码:');
  console.log('='.repeat(80));
  console.log(generatedCode);
  console.log('='.repeat(80));
  
  // 验证优化效果
  const codeLines = generatedCode.split('\n');
  
  // 检查是否移除了source对象
  const elementsLine = codeLines.find(line => line.includes('_bitmap.elements = '));
  if (elementsLine) {
    const hasSourceInElements = elementsLine.includes('"source"');
    console.log(`✅ Elements数组中${hasSourceInElements ? '仍包含' : '已移除'}source对象`);
  }
  
  // 检查图片加载优化
  const imageLoadLines = codeLines.filter(line => line.includes('ImageManager.loadBitmapFromUrl'));
  console.log(`✅ 图片加载次数: ${imageLoadLines.length} (优化前应该是4次，优化后应该是2次)`);
  
  // 检查是否有重复的图片路径
  const actor1LoadCount = codeLines.filter(line => 
    line.includes('Actor1.png') && line.includes('ImageManager.loadBitmapFromUrl')
  ).length;
  console.log(`✅ Actor1.png 加载次数: ${actor1LoadCount} (应该只有1次)`);
  
  const actor1_3LoadCount = codeLines.filter(line => 
    line.includes('Actor1_3.png') && line.includes('ImageManager.loadBitmapFromUrl')
  ).length;
  console.log(`✅ Actor1_3.png 加载次数: ${actor1_3LoadCount} (应该只有1次)`);
  
  // 检查是否为所有相同图片的元素设置了source
  const sourceSetLines = codeLines.filter(line => line.includes('.source = bitmap;'));
  console.log(`✅ source设置次数: ${sourceSetLines.length} (应该是4次，对应4个图片元素)`);
  
  // 检查重绘调用
  const redrawingLines = codeLines.filter(line => line.includes('._bitmap.redrawing()'));
  console.log(`✅ 重绘调用次数: ${redrawingLines.length} (应该是2次，对应2个唯一图片)`);
  
  console.log('🎉 测试完成！');
}

/**
 * 测试只有文本元素的情况
 */
export function testTextOnlyElements(): void {
  console.log('🧪 测试只有文本元素的情况...');

  const textOnlyElements = [
    {
      "type": "text",
      "text": "标题",
      "x": 100,
      "y": 50,
      "maxWidth": 200,
      "lineHeight": 36,
      "align": "center"
    },
    {
      "type": "text",
      "text": "副标题",
      "x": 100,
      "y": 100,
      "maxWidth": 200,
      "lineHeight": 24,
      "align": "center"
    }
  ];

  const testObj = new BaseTempObj(['Scene_Title', '0'], 'Sprite', 'modify');
  testObj.setProperty('_bitmap.elements', textOnlyElements);
  
  const generatedCode = testObj.generateCode();
  
  console.log('只有文本的生成代码:');
  console.log('='.repeat(50));
  console.log(generatedCode);
  console.log('='.repeat(50));
  
  // 验证没有图片加载代码
  const hasImageLoad = generatedCode.includes('ImageManager.loadBitmapFromUrl');
  console.log(`✅ ${hasImageLoad ? '错误：包含' : '正确：不包含'}图片加载代码`);
  
  console.log('🎉 文本测试完成！');
}

// 如果在浏览器环境中，自动运行测试
if (typeof window !== 'undefined') {
  setTimeout(() => {
    testOptimizedBitmapElements();
    console.log('\n');
    testTextOnlyElements();
  }, 1000);
}
