import { setObjectProperty } from '../../../services/index';

// ==================== 对象方法管理相关方法 ====================

/**
 * 将JavaScript代码字符串转换为方法并赋值给对象的指定属性
 * @param object 要修改的对象
 * @param propertyName 要赋值的属性名
 * @param codeString JavaScript代码字符串
 * @returns 是否成功设置方法
 */
export function setObjectMethod(object: any, propertyName: string, codeString: string): boolean {
  if (!object) {
    console.error('ObjectMethod: 对象为空，无法设置方法');
    return false;
  }

  try {
    console.log(`ObjectMethod: 为对象设置方法 ${propertyName}，代码长度: ${codeString.length}`);

    // 处理代码字符串，确保它是一个有效的函数表达式
    let processedCode = codeString.trim();

    // 移除注释行，便于检查代码结构
    const codeWithoutComments = processedCode.replace(/\/\/.*$/gm, '').trim();

    // 检查代码是否已经是一个完整的函数表达式
    if (codeWithoutComments.startsWith('(function') && codeWithoutComments.endsWith(')')) {
      // 已经是正确的函数表达式，不需要修改
      console.log('ObjectMethod: 代码已经是函数表达式，不需要修改');
    } else if (codeWithoutComments.startsWith('function(') || codeWithoutComments.startsWith('function (')) {
      // 是函数声明但没有名称，需要转换为函数表达式
      processedCode = `(${processedCode})`;
      console.log('ObjectMethod: 将函数声明转换为函数表达式');
    } else {
      // 其他情况，包装在函数表达式中
      console.log('ObjectMethod: 将代码包装在函数表达式中');
      processedCode = `(function(event) {
        ${processedCode}
      })`;
    }

    // 创建事件处理函数
    const createEventHandler = new Function(`
      // 直接从window获取必要的引用
      const SceneManager = window.SceneManager;
      const Scene_Title = window.Scene_Title;
      const Scene_Map = window.Scene_Map;
      const Scene_Battle = window.Scene_Battle;
      const Scene_Menu = window.Scene_Menu;
      const Scene_Item = window.Scene_Item;
      const Scene_Skill = window.Scene_Skill;
      const Scene_Status = window.Scene_Status;
      const Scene_Options = window.Scene_Options;
      const Scene_Save = window.Scene_Save;
      const Scene_Load = window.Scene_Load;
      const Scene_GameEnd = window.Scene_GameEnd;
      const TouchInput = window.TouchInput;
      const Point = window.Point;

      // 返回事件处理函数
      return ${processedCode};
    `);

    // 创建事件处理函数并赋值给对象的属性
    try {
      const eventHandler = createEventHandler();

      // 使用 Object.defineProperty 确保属性被正确设置
      Object.defineProperty(object, propertyName, {
        value: eventHandler,
        writable: true,
        enumerable: true,
        configurable: true
      });

      // 验证属性是否成功设置
      if (typeof object[propertyName] === 'function') {
        console.log(`ObjectMethod: 成功设置对象的 ${propertyName} 方法`, object);
        return true;
      } else {
        // 如果使用 Object.defineProperty 失败，尝试直接赋值
        object[propertyName] = eventHandler;

        // 再次验证
        if (typeof object[propertyName] === 'function') {
          console.log(`ObjectMethod: 通过直接赋值成功设置对象的 ${propertyName} 方法`, object);
          return true;
        } else {
          console.error(`ObjectMethod: 无法设置对象的 ${propertyName} 方法，可能是对象不允许添加新属性`);

          // 尝试最后一种方法：如果对象有原型，尝试在原型上设置方法
          const proto = Object.getPrototypeOf(object);
          if (proto) {
            Object.defineProperty(proto, propertyName, {
              value: eventHandler,
              writable: true,
              enumerable: true,
              configurable: true
            });

            if (typeof object[propertyName] === 'function') {
              console.log(`ObjectMethod: 通过原型成功设置对象的 ${propertyName} 方法`, object);
              return true;
            }
          }

          return false;
        }
      }
    } catch (error) {
      console.error(`ObjectMethod: 创建事件处理函数失败:`, error);
      return false;
    }
  } catch (error) {
    console.error(`ObjectMethod: 设置对象方法 ${propertyName} 失败:`, error);
    return false;
  }
}

/**
 * 执行对象的方法
 * @param object 要执行方法的对象
 * @param methodName 方法名称
 * @param args 方法参数数组
 * @returns 方法执行的结果
 */
export function executeMethod(object: any, methodName: string, args: any[] = []): any {
  if (!object) {
    console.error('ObjectMethod: 对象为空，无法执行方法');
    return null;
  }

  try {
    console.log(`ObjectMethod: 执行对象方法 ${methodName}，参数:`, args);

    // 检查对象是否有该方法
    if (typeof object[methodName] !== 'function') {
      console.error(`ObjectMethod: 对象没有方法 ${methodName}`);
      return null;
    }

    // 执行方法
    const result = object[methodName](...args);
    console.log(`ObjectMethod: 方法 ${methodName} 执行成功，结果:`, result);
    return result;
  } catch (error) {
    console.error(`ObjectMethod: 执行方法 ${methodName} 失败:`, error);
    return null;
  }
}

/**
 * 记录对象方法修改到后端
 * @param object 要修改的对象
 * @param methodName 方法名称
 * @param codeString 方法代码
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export async function recordObjectMethodModification(
  object: any,
  methodName: string,
  codeString: string
): Promise<string> {
  console.log(`记录对象方法修改: ${methodName} = ${codeString.substring(0, 100)}...`);

  // 使用统一的后端记录方法
  // 对于方法修改，将方法代码作为value传递
  setObjectProperty(
    (object as any)._rpgEditorPath || ['Unknown'],
    object.constructor?.name || 'Unknown',
    methodName,
    codeString
  );
  return 'success';
}

/**
 * 获取对象方法的当前代码
 * @param object 对象
 * @param methodName 方法名称
 * @returns 方法代码字符串
 */
export function getObjectMethodCode(object: any, methodName: string): string {
  if (!object || typeof object[methodName] !== 'function') {
    return '';
  }

  try {
    return object[methodName].toString();
  } catch (error) {
    console.error(`获取对象方法 ${methodName} 代码失败:`, error);
    return '';
  }
}

/**
 * 检查对象是否有指定方法
 * @param object 对象
 * @param methodName 方法名称
 * @returns 是否有该方法
 */
export function hasObjectMethod(object: any, methodName: string): boolean {
  return object && typeof object[methodName] === 'function';
}

/**
 * 删除对象的方法
 * @param object 对象
 * @param methodName 方法名称
 * @returns 是否成功删除
 */
export function removeObjectMethod(object: any, methodName: string): boolean {
  if (!object) {
    console.error('ObjectMethod: 对象为空，无法删除方法');
    return false;
  }

  try {
    if (object.hasOwnProperty(methodName)) {
      delete object[methodName];
      console.log(`ObjectMethod: 成功删除对象的 ${methodName} 方法`);
      return true;
    } else {
      console.warn(`ObjectMethod: 对象没有 ${methodName} 方法，无需删除`);
      return false;
    }
  } catch (error) {
    console.error(`ObjectMethod: 删除对象方法 ${methodName} 失败:`, error);
    return false;
  }
}
