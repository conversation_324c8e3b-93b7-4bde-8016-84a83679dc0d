/**
 * 防抖函数工具
 */

export interface PropertyUpdate {
  object: any;
  objectPath: string[];
  className: string;
  fieldName: string;
  newValue: any;
  oldValue?: any;
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(() => {
      func(...args);
    }, wait);
  };
}

/**
 * 合并同一对象的多个属性更新
 */
export function mergePropertyUpdates(updates: PropertyUpdate[]): PropertyUpdate[] {
  const mergedMap = new Map<string, PropertyUpdate>();
  
  for (const update of updates) {
    const key = `${update.className}_${update.objectPath.join('_')}_${update.fieldName}`;
    
    // 如果已存在相同对象的相同属性更新，则合并
    if (mergedMap.has(key)) {
      const existing = mergedMap.get(key)!;
      // 保留最初的 oldValue，使用最新的 newValue
      mergedMap.set(key, {
        ...update,
        oldValue: existing.oldValue
      });
    } else {
      mergedMap.set(key, update);
    }
  }
  
  return Array.from(mergedMap.values());
}

/**
 * 批量属性更新队列
 */
class PropertyUpdateQueue {
  private queue: PropertyUpdate[] = [];
  private processing = false;
  
  add(update: PropertyUpdate): void {
    this.queue.push(update);
  }
  
  flush(): PropertyUpdate[] {
    if (this.processing) return [];
    
    this.processing = true;
    const updates = [...this.queue];
    this.queue = [];
    this.processing = false;
    
    return mergePropertyUpdates(updates);
  }
  
  clear(): void {
    this.queue = [];
  }
  
  get size(): number {
    return this.queue.length;
  }
}

export const globalPropertyUpdateQueue = new PropertyUpdateQueue();
