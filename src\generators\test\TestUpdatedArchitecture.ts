/**
 * 测试更新后的generators架构
 * 验证Set集合记录、统一合成代码、查找对象方法和生成对象方法面板代码
 */

import { 
  BaseTempObj, 
  OperationManager,
  setObjectProperty,
  createOperation
} from '../index';

import { BackendService } from '../../services/BackendService';

export function testUpdatedArchitecture(): void {
  console.log('🧪 测试更新后的generators架构...');
  
  // 测试1: BaseTempObj的Set集合记录
  console.log('\n1. 测试BaseTempObj的Set集合记录...');
  const obj1 = new BaseTempObj(['Scene_Title', '2'], 'Sprite', 'modify');
  
  // 设置属性
  obj1.setProperty('x', 100);
  obj1.setProperty('y', 200);
  obj1.setProperty('alpha', 0.8);
  
  console.log('- 修改的属性名称集合:', obj1.getModifiedPropertyNames());
  console.log('- 检查x属性是否修改:', obj1.isPropertyModified('x'));
  console.log('- 检查z属性是否修改:', obj1.isPropertyModified('z'));
  
  // 清除一个属性
  obj1.clearPropertyModification('y');
  console.log('- 清除y属性后的修改集合:', obj1.getModifiedPropertyNames());
  
  // 测试2: 复杂的bitmap.elements处理
  console.log('\n2. 测试复杂的bitmap.elements处理...');
  const obj2 = new BaseTempObj(['Scene_Menu', '1', '3', '1', '2'], 'Sprite', 'modify');
  
  const elementsValue = [
    {
      "type": "text",
      "text": "里德",
      "x": 184,
      "y": 13,
      "maxWidth": 168,
      "lineHeight": 36,
      "align": "left"
    },
    {
      "type": "image",
      "source": {
        "_url": "../projects/Project4/img/faces/Actor1.png",
        "width": 144,
        "height": 129
      },
      "sx": 432,
      "sy": 7,
      "sw": 144,
      "sh": 129,
      "dx": 5,
      "dy": 138,
      "dw": 144,
      "dh": 129
    }
  ];
  
  obj2.setProperty('_bitmap.elements', elementsValue);
  obj2.setProperty('_bitmap.fontSize', 24);
  obj2.setProperty('_bitmap.textColor', '#ffffff');
  
  console.log('- bitmap对象修改的属性:', obj2.getModifiedPropertyNames());
  
  // 测试3: OperationManager的统一合成代码
  console.log('\n3. 测试OperationManager的统一合成代码...');
  const manager = new OperationManager();
  
  // 添加多个操作
  manager.addOperation(['Scene_Title', '1'], 'Sprite');
  manager.setProperty(['Scene_Title', '1'], 'Sprite', 'x', 50);
  manager.setProperty(['Scene_Title', '1'], 'Sprite', 'y', 75);
  manager.setProperty(['Scene_Title', '1'], 'Sprite', 'alpha', 0.9);
  
  manager.addOperation(['Scene_Menu', '2'], 'Container');
  manager.setProperty(['Scene_Menu', '2'], 'Container', 'visible', false);
  manager.setProperty(['Scene_Menu', '2'], 'Container', 'rotation', 0.5);
  
  manager.addOperation(['Scene_Battle', '3'], 'Sprite');
  manager.setProperty(['Scene_Battle', '3'], 'Sprite', 'tint', 0xff0000);
  manager.setProperty(['Scene_Battle', '3'], 'Sprite', 'colorTone', [255, 0, 0, 128]);
  
  // 生成统一合成代码
  const unifiedCode = manager.generateUnifiedCode();
  console.log('统一合成代码:');
  console.log('='.repeat(80));
  console.log(unifiedCode);
  console.log('='.repeat(80));
  
  // 测试4: 验证代码结构
  console.log('\n4. 验证代码结构...');
  const codeLines = unifiedCode.split('\n');
  
  // 检查是否包含必要的方法
  const hasFindObjectMethod = unifiedCode.includes('function findObjectByScenePath');
  const hasGetObjectPathMethod = unifiedCode.includes('function getObjectPath');
  const hasCreateGameObjectMethod = unifiedCode.includes('function createGameObject');
  const hasAddObjectToParentMethod = unifiedCode.includes('function addObjectToParent');
  const hasRemoveObjectMethod = unifiedCode.includes('function removeObjectFromParent');
  
  console.log('- 包含findObjectByScenePath方法:', hasFindObjectMethod ? '✅' : '❌');
  console.log('- 包含getObjectPath方法:', hasGetObjectPathMethod ? '✅' : '❌');
  console.log('- 包含createGameObject方法:', hasCreateGameObjectMethod ? '✅' : '❌');
  console.log('- 包含addObjectToParent方法:', hasAddObjectToParentMethod ? '✅' : '❌');
  console.log('- 包含removeObjectFromParent方法:', hasRemoveObjectMethod ? '✅' : '❌');
  
  // 检查场景修改代码
  const hasSceneTitleModification = unifiedCode.includes('Scene_Title.prototype.start');
  const hasSceneMenuModification = unifiedCode.includes('Scene_Menu.prototype.start');
  const hasSceneBattleModification = unifiedCode.includes('Scene_Battle.prototype.start');
  
  console.log('- 包含Scene_Title修改:', hasSceneTitleModification ? '✅' : '❌');
  console.log('- 包含Scene_Menu修改:', hasSceneMenuModification ? '✅' : '❌');
  console.log('- 包含Scene_Battle修改:', hasSceneBattleModification ? '✅' : '❌');
  
  // 测试5: 序列化和反序列化
  console.log('\n5. 测试序列化和反序列化...');
  const serialized = manager.toJSON();
  console.log('- 序列化数据键数量:', Object.keys(serialized).length);
  
  const newManager = new OperationManager();
  newManager.fromJSON(serialized);
  console.log('- 反序列化后操作数量:', newManager.size());
  
  // 验证反序列化后的Set集合
  const restoredOperations = newManager.getAllOperations();
  for (const op of restoredOperations) {
    console.log(`- 操作 ${op.getUniqueKey()} 修改的属性:`, op.getModifiedPropertyNames());
  }
  
  // 测试6: BackendService集成
  console.log('\n6. 测试BackendService集成...');
  console.log('- BackendService类型:', typeof BackendService);
  console.log('- saveCode方法:', typeof BackendService.saveCode);
  console.log('- refreshPreview方法:', typeof BackendService.refreshPreview);
  console.log('- saveAndRefresh方法:', typeof BackendService.saveAndRefresh);
  
  // 测试7: 统计信息
  console.log('\n7. 测试统计信息...');
  const stats = manager.getStatistics();
  console.log('- 统计信息:', stats);
  
  // 测试8: 按场景分组
  console.log('\n8. 测试按场景分组...');
  const sceneGroups = manager.groupOperationsByScene();
  console.log('- 场景数量:', sceneGroups.size);
  for (const [sceneName, operations] of sceneGroups) {
    console.log(`- 场景 ${sceneName}: ${operations.length} 个操作`);
  }
  
  // 测试9: 清除操作
  console.log('\n9. 测试清除操作...');
  const obj3 = createOperation(['Test', '1'], 'Sprite');
  obj3.setProperty('x', 100);
  obj3.setProperty('y', 200);
  obj3.setProperty('z', 300);
  
  console.log('- 清除前修改的属性:', obj3.getModifiedPropertyNames());
  obj3.clearAllModifications();
  console.log('- 清除后修改的属性:', obj3.getModifiedPropertyNames());
  
  console.log('\n✅ 更新后的架构测试完成！');
  
  // 总结
  console.log('\n📋 测试总结:');
  console.log('✅ BaseTempObj使用Set集合记录属性修改');
  console.log('✅ OperationManager生成统一合成代码');
  console.log('✅ 包含完整的对象查找方法');
  console.log('✅ 包含完整的对象创建方法面板代码');
  console.log('✅ 支持按场景分组生成代码');
  console.log('✅ BackendService统一后端交互API');
  console.log('✅ 序列化和反序列化正常工作');
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testUpdatedArchitecture();
}
