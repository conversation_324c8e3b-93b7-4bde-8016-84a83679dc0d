/**
 * 属性优化测试
 * 测试新的属性变化流程是否正常工作
 */

import { PropertyProcessor } from '../components/property/propertyProcess';
import { globalOperationManager } from '../generators';

// 模拟对象
class MockDisplayObject {
  public x: number = 0;
  public y: number = 0;
  public width: number = 100;
  public height: number = 100;
  public alpha: number = 1;
  public visible: boolean = true;
  public constructor() {
    this.constructor.name = 'MockDisplayObject';
  }
}

// 模拟 Sprite 对象
class MockSprite extends MockDisplayObject {
  public hue: number = 0;
  public colorTone: [number, number, number, number] = [0, 0, 0, 0];
  public blendColor: [number, number, number, number] = [0, 0, 0, 0];
  
  constructor() {
    super();
    this.constructor.name = 'MockSprite';
  }
  
  setHue(hue: number): void {
    this.hue = hue;
  }
  
  setColorTone(colorTone: [number, number, number, number]): void {
    this.colorTone = [...colorTone];
  }
  
  setBlendColor(blendColor: [number, number, number, number]): void {
    this.blendColor = [...blendColor];
  }
}

/**
 * 测试基础属性处理
 */
export function testBasicPropertyProcessing(): boolean {
  console.log('🧪 测试基础属性处理...');
  
  try {
    const mockObject = new MockDisplayObject();
    const originalX = mockObject.x;
    
    // 测试基础属性设置
    PropertyProcessor.applyProperty(mockObject, 'x', 150);
    
    if (mockObject.x !== 150) {
      console.error('❌ 基础属性设置失败: x 值未正确更新');
      return false;
    }
    
    console.log('✅ 基础属性处理测试通过');
    return true;
  } catch (error) {
    console.error('❌ 基础属性处理测试失败:', error);
    return false;
  }
}

/**
 * 测试 Sprite 颜色属性处理
 */
export function testSpriteColorProcessing(): boolean {
  console.log('🧪 测试 Sprite 颜色属性处理...');
  
  try {
    const mockSprite = new MockSprite();
    
    // 测试色相设置
    PropertyProcessor.applyProperty(mockSprite, 'hue', 180);
    
    if (mockSprite.hue !== 180) {
      console.error('❌ Sprite 色相设置失败: hue 值未正确更新');
      return false;
    }
    
    // 测试色调设置
    const newColorTone: [number, number, number, number] = [50, -50, 0, 0];
    PropertyProcessor.applyProperty(mockSprite, 'colorTone', newColorTone);
    
    if (!arraysEqual(mockSprite.colorTone, newColorTone)) {
      console.error('❌ Sprite 色调设置失败: colorTone 值未正确更新');
      return false;
    }
    
    console.log('✅ Sprite 颜色属性处理测试通过');
    return true;
  } catch (error) {
    console.error('❌ Sprite 颜色属性处理测试失败:', error);
    return false;
  }
}

/**
 * 测试操作记录功能
 */
export function testOperationRecording(): boolean {
  console.log('🧪 测试操作记录功能...');
  
  try {
    // 清空操作管理器
    globalOperationManager.clear();
    
    const testPath = ['Scene_Test', '0'];
    const testClassName = 'MockSprite';
    
    // 记录一个属性操作
    globalOperationManager.setProperty(testPath, testClassName, 'x', 200);
    
    // 检查操作是否被记录
    const operation = globalOperationManager.getOperation(testPath, testClassName);
    if (!operation) {
      console.error('❌ 操作记录失败: 未找到记录的操作');
      return false;
    }
    
    // 检查属性值是否正确
    const recordedValue = operation.getProperty('x');
    if (recordedValue !== 200) {
      console.error('❌ 操作记录失败: 记录的属性值不正确');
      return false;
    }
    
    console.log('✅ 操作记录功能测试通过');
    return true;
  } catch (error) {
    console.error('❌ 操作记录功能测试失败:', error);
    return false;
  }
}

/**
 * 测试批量属性处理
 */
export function testBatchPropertyProcessing(): boolean {
  console.log('🧪 测试批量属性处理...');
  
  try {
    const mockObjects = [
      new MockDisplayObject(),
      new MockDisplayObject(),
      new MockSprite()
    ];
    
    // 模拟批量更新
    const updates = mockObjects.map((obj, index) => ({
      object: obj,
      objectPath: ['Scene_Test', index.toString()],
      className: obj.constructor.name,
      fieldName: 'x',
      newValue: 100 + index * 50,
      oldValue: obj.x
    }));
    
    // 应用批量更新
    for (const update of updates) {
      PropertyProcessor.applyProperty(
        update.object,
        update.fieldName as any,
        update.newValue
      );
      
      globalOperationManager.setProperty(
        update.objectPath,
        update.className,
        update.fieldName,
        update.newValue
      );
    }
    
    // 验证结果
    for (let i = 0; i < mockObjects.length; i++) {
      const expectedValue = 100 + i * 50;
      if ((mockObjects[i] as any).x !== expectedValue) {
        console.error(`❌ 批量属性处理失败: 对象 ${i} 的 x 值不正确`);
        return false;
      }
    }
    
    console.log('✅ 批量属性处理测试通过');
    return true;
  } catch (error) {
    console.error('❌ 批量属性处理测试失败:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
export function runAllTests(): boolean {
  console.log('🚀 开始运行属性优化测试...');
  
  const tests = [
    testBasicPropertyProcessing,
    testSpriteColorProcessing,
    testOperationRecording,
    testBatchPropertyProcessing
  ];
  
  let passedCount = 0;
  
  for (const test of tests) {
    if (test()) {
      passedCount++;
    }
  }
  
  const totalTests = tests.length;
  const success = passedCount === totalTests;
  
  console.log(`📊 测试结果: ${passedCount}/${totalTests} 通过`);
  
  if (success) {
    console.log('🎉 所有测试通过！属性优化功能正常工作。');
  } else {
    console.log('⚠️ 部分测试失败，需要检查实现。');
  }
  
  return success;
}

// 辅助函数
function arraysEqual(a: any[], b: any[]): boolean {
  if (a.length !== b.length) return false;
  for (let i = 0; i < a.length; i++) {
    if (a[i] !== b[i]) return false;
  }
  return true;
}

// 如果在浏览器环境中，自动运行测试
if (typeof window !== 'undefined') {
  // 延迟运行，确保所有模块都已加载
  setTimeout(() => {
    runAllTests();
  }, 1000);
}
