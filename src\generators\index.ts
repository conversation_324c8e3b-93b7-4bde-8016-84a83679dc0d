/**
 * Generators 主入口文件
 * 重新实现的generators架构，基于BaseTempObj核心类
 */

export { BaseTempObj } from './core/baseTempObj';
export { OperationManager } from './core/OperationManager';

// 创建全局操作管理器实例
import { OperationManager } from './core/OperationManager';

export const globalOperationManager = new OperationManager();

/**
 * 便捷方法：设置对象属性
 */
export function setObjectProperty(
  objectPath: string[],
  className: string,
  propertyName: string,
  value: any
): void {
  globalOperationManager.setProperty(objectPath, className, propertyName, value);
}

/**
 * 便捷方法：获取对象属性
 */
export function getObjectProperty(
  objectPath: string[],
  className: string,
  propertyName: string
): any {
  return globalOperationManager.getProperty(objectPath, className, propertyName);
}

/**
 * 便捷方法：生成所有代码
 */
export function generateAllCode(): string {
  return globalOperationManager.generateAllCode();
}

/**
 * 便捷方法：生成按场景分组的代码
 */
export function generateCodeByScene(): Map<string, string> {
  return globalOperationManager.generateCodeByScene();
}

/**
 * 便捷方法：清空所有操作
 */
export function clearAllOperations(): void {
  globalOperationManager.clear();
}

/**
 * 便捷方法：获取操作统计信息
 */
export function getOperationStatistics() {
  return globalOperationManager.getStatistics();
}

/**
 * 便捷方法：序列化操作
 */
export function serializeOperations(): string {
  return JSON.stringify(globalOperationManager.toJSON(), null, 2);
}

/**
 * 便捷方法：反序列化操作
 */
export function deserializeOperations(jsonString: string): void {
  try {
    const data = JSON.parse(jsonString);
    globalOperationManager.fromJSON(data);
  } catch (error) {
    console.error('反序列化操作失败:', error);
    throw error;
  }
}

/**
 * 便捷方法：创建BaseTempObj实例
 */
export function createOperation(
  objectPath: string[],
  className: string,
  operationType: 'modify' | 'create' | 'delete' = 'modify'
) {
  return globalOperationManager.addOperation(objectPath, className, operationType);
}

/**
 * 便捷方法：获取操作实例
 */
export function getOperation(objectPath: string[], className: string) {
  return globalOperationManager.getOperation(objectPath, className);
}

/**
 * 便捷方法：删除操作
 */
export function removeOperation(objectPath: string[], className: string): boolean {
  return globalOperationManager.removeOperation(objectPath, className);
}

/**
 * 便捷方法：检查是否有操作
 */
export function hasOperation(objectPath: string[], className: string): boolean {
  return globalOperationManager.hasOperation(objectPath, className);
}

/**
 * 便捷方法：获取所有操作
 */
export function getAllOperations() {
  return globalOperationManager.getAllOperations();
}

/**
 * 便捷方法：获取修改的操作
 */
export function getModifiedOperations() {
  return globalOperationManager.getModifiedOperations();
}

// 导出类型定义已移除，现在使用Map存储属性


