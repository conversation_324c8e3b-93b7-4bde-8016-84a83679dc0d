export class BaseInfoProcessor {
  private static readonly PROPERTY_MAP = {
    x: 'x',
    y: 'y',
    width: 'width',
    height: 'height',
    visible: 'visible',
    alpha: 'alpha',
    rotation: 'rotation',
    scale: 'scale',
    anchor: 'anchor',
    skewX: 'skew.x',
    skewY: 'skew.y',
    tint: 'tint',
    blendMode: 'blendMode',
    filters: 'filters',
    mask: 'mask',
    interactive: 'interactive',
    buttonMode: 'buttonMode',
    cursor: 'cursor',
    hitArea: 'hitArea',
    eventMode: 'eventMode',
    sortableChildren: 'sortableChildren',
    sortDirty: 'sortDirty',
    zIndex: 'zIndex',
    zOrder: 'zOrder',
    z: 'z',
    name: 'name'
  };

  private static readonly DEFAULT_VALUES = {
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    visible: true,
    alpha: 1,
    rotation: 0,
    scale: { x: 1, y: 1 },
    anchor: { x: 0.5, y: 0.5 },
    skewX: 0,
    skewY: 0,
    tint: 0xFFFFFF,
    blendMode: 0,
    filters: [],
    mask: null,
    interactive: false,
    buttonMode: false,
    cursor: 'pointer',
    hitArea: null,
    eventMode: 'none',
    sortableChildren: false,
    sortDirty: false,
    zIndex: 0,
    zOrder: 0,
    z: 0,
    name: ''
  };

  private static readonly PROPERTY_TYPES = {
    x: 'number',
    y: 'number',
    width: 'number',
    height: 'number',
    visible: 'boolean',
    alpha: 'number',
    rotation: 'number',
    scale: 'object',
    anchor: 'object',
    skewX: 'number',
    skewY: 'number',
    tint: 'number',
    blendMode: 'number',
    filters: 'array',
    mask: 'object',
    interactive: 'boolean',
    buttonMode: 'boolean',
    cursor: 'string',
    hitArea: 'object',
    eventMode: 'string',
    sortableChildren: 'boolean',
    sortDirty: 'boolean',
    zIndex: 'number',
    zOrder: 'number',
    z: 'number',
    name: 'string'
  };

  private static readonly PROPERTY_DESCRIPTIONS = {
    x: 'X坐标',
    y: 'Y坐标',
    width: '宽度',
    height: '高度',
    visible: '是否可见',
    alpha: '透明度',
    rotation: '旋转角度',
    scale: '缩放比例',
    anchor: '锚点位置',
    skewX: 'X轴倾斜角度',
    skewY: 'Y轴倾斜角度',
    tint: '色调',
    blendMode: '混合模式',
    filters: '滤镜',
    mask: '遮罩',
    interactive: '是否可交互',
    buttonMode: '按钮模式',
    cursor: '鼠标样式',
    hitArea: '点击区域',
    eventMode: '事件模式',
    sortableChildren: '子对象可排序',
    sortDirty: '排序标记',
    zIndex: 'Z轴索引',
    zOrder: 'Z轴顺序',
    z: 'Z轴位置',
    name: '对象名称'
  };

  /**
   * 生成bitmap加载和赋值的代码
   * @param value bitmap的URL或配置
   * @returns 生成的代码行数组
   */
  private generateBitmapCode(value: any): string[] {
    const lines: string[] = [];
    const variableName = this.generateVariableName();

    if (typeof value === 'string') {
      // 如果是URL字符串，直接加载
      lines.push(`    // 加载bitmap图片`);
      lines.push(`    const ${variableName} = ImageManager.loadBitmapFromUrl("${value}");`);
      lines.push(`    ${variableName}.addLoadListener(function(bitmap) {`);
      lines.push(`        if (DEBUG) console.log('[Bitmap] 图片加载完成:', "${value}");`);
      lines.push(`        this.bitmap = bitmap;`);
      lines.push(`    }.bind(this));`);
    } else if (typeof value === 'object') {
      // 如果是配置对象
      if (value.url) {
        lines.push(`    // 加载bitmap图片`);
        lines.push(`    const ${variableName} = ImageManager.loadBitmapFromUrl("${value.url}");`);
        lines.push(`    ${variableName}.addLoadListener(function(bitmap) {`);
        lines.push(`        if (DEBUG) console.log('[Bitmap] 图片加载完成:', "${value.url}");`);

        // 应用其他配置
        if (value.width || value.height) {
          lines.push(`        bitmap.resize(${value.width || 'bitmap.width'}, ${value.height || 'bitmap.height'});`);
        }
        if (value.smooth !== undefined) {
          lines.push(`        bitmap.smooth = ${value.smooth};`);
        }

        lines.push(`        this.bitmap = bitmap;`);
        lines.push(`    }.bind(this));`);
      } else if (value.elements) {
        // 处理elements数组
        lines.push(`    // 设置bitmap elements数组`);
        lines.push(`    const elements = ${JSON.stringify(value.elements)};`);
        lines.push(`    this._bitmap.elements = elements;`);

        // 为每个元素加载图片
        lines.push(`    elements.forEach((element, index) => {`);
        lines.push(`        if (element.url) {`);
        lines.push(`            const imageBitmap = ImageManager.loadBitmapFromUrl(element.url);`);
        lines.push(`            imageBitmap.addLoadListener(function(bitmap) {`);
        lines.push(`                if (DEBUG) console.log('[Bitmap] 元素图片加载完成:', element.url);`);
        lines.push(`                if (this._bitmap.elements[index]) {`);
        lines.push(`                    this._bitmap.elements[index].source = bitmap;`);
        lines.push(`                }`);
        lines.push(`            }.bind(this));`);
        lines.push(`        }`);
        lines.push(`    }.bind(this));`);
      }
    }

    return lines;
  }

  /**
   * 生成变量名
   */
  private generateVariableName(): string {
    return `bitmap_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 处理bitmap属性
   */
  public processBitmapProperty(value: any): string[] {
    return this.generateBitmapCode(value);
  }
} 